<!--
  * @FileDescription：绑定表单组合套
-->
<template>
  <hos-dialog :title="$t('绑定表单组合套')" :visible.sync="dialogVisible" :append-to-body="true" width="550px">
    <empty-box v-if="groups.length === 0" :desc="$t('暂无未绑定的表单')" />
    <hos-checkbox-group v-else v-model="groupsSelected">
      <template v-for="(group, index) in groups">
        <div :key="index" class="group-box">
          <div class="group-box-title">
            <hos-checkbox :label="group.name">{{ group.name }}</hos-checkbox>
          </div>
          <div class="form-items">
            <hos-tag
              v-for="(form, i) in group.formInfos"
              :key="i"
              style="margin-right: 10px; margin-bottom: 10px"
              :effect="groupsSelected.indexOf(group.name) >= 0 ? 'dark' : 'light'"
              >{{ form.formName }}</hos-tag
            >
            <!-- <hos-checkbox-group :value="groupsSelected.indexOf(group.name)>=0?group.formInfos.map(form=>form.formId):[]">
              <hos-checkbox v-for="(form,i) in group.formInfos" :key="i" :label="form.formId">{{ form.formName }}</hos-checkbox>
            </hos-checkbox-group> -->
          </div>
        </div>
      </template>
    </hos-checkbox-group>

    <div slot="footer">
      <hos-button size="mini" @click="dialogVisible = false">{{ $t('取消') }}</hos-button>
      <hos-button size="mini" type="primary" @click="confirm">{{ $t('确定') }}</hos-button>
    </div>
  </hos-dialog>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      dialogVisible: false,
      params: {},
      groupsSelected: [],
      projectFormList: [],
      groups: []
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.groupsSelected = []
        this.groups = []
      }
    }
  },
  methods: {
    async init(params) {
      this.$api('biz.edc.project.form.queryAllListApi', {
        subjectId: this.subjectId,
        isPublish: 1
      })
        .then((response) => {
          response.data = response.data || []
          this.projectFormList = response.data.map((item) => ({ ...item, formId: item.id }))

          this.params = params
          this.dialogVisible = true
          this.$api('biz.edc.patient.patientView.getVisitFormGroup', params).then((res) => {
            const data = res.data
            const groups = data || []
            groups.forEach((group) => {
              if (!group.formInfos || (group.formInfos.length == 0 && group.formIds && group.formIds.length > 0)) {
                const formInfos = []
                group.formIds.forEach((formId) => {
                  const tmp = this.projectFormList.filter((item) => item.formId == formId)
                  if (tmp.length > 0) {
                    formInfos.push(this.deepClone(tmp[0]))
                  }
                })
                group.formInfos = formInfos
              }
            })
            this.groups = groups
          })
        })
        .catch(() => {})
    },
    confirm() {
      const formIds = []
      this.groups.forEach((g) => {
        if (this.groupsSelected.indexOf(g.name) >= 0) {
          formIds.push(...g.formInfos.map((form) => form.id))
        }
      })
      if (formIds.length === 0) {
        this.$message.error(this.$t('请选择一个组合套'))
        return
      }
      this.$emit('confirm', { visitId: this.params.visitId, formIds: formIds })
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.group-box {
  padding: 10px;
}
.group-box-title {
  margin-bottom: 10px;
}
.form-items {
  margin-bottom: 10px;
}
</style>