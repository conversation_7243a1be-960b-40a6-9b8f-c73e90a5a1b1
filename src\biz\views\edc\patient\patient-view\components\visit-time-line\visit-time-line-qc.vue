<!--
  * @FileDescription：随访时间轴
-->
<template>
  <section v-loading="timeLineLoading" class="visit-time-line">
    <div class="period-list-container">
      <template>
				<el-button v-if="!isPreview && wholeBtnShow" type="plain" class="whole-time-line-btn" @click="seeWholeLine">查看完整时间轴</el-button>
				<el-button v-if="!isPreview && !wholeBtnShow" type="plain" class="whole-time-line-btn" @click="seeCurrentLine">查看当前时间轴</el-button>
			</template>
      <div v-for="(period, i) in items" :key="i" class="period-item"
      :class="{
					'quancheng-period-item': true,
					'first-period-node': judgeFirstPeriodNode(period, items),
					'last-period-node':  judgeLastPeriodNode(period, i, items)
				}">
        <div v-if="judgeFirstPeriodNode(period, items) && period.diseaseShowName" class="period-disease-name">{{ period.diseaseShowName }}</div>
        <div class="top-period-info">
          <span class="period-name" :title="period.periodName">
            <span>{{ period.periodName }}</span>
            <span v-if="refSetBtnShow(period)" class="set-refer-date" @click="setReferDate(period)">
              <i :title="$t('设置参考点时间')" class="hos-icom-cal"></i>
            </span>
          </span>
          <span>
            <i
              v-if="!isPreview && period.allowStop && period.periodStatus !== 1 && $hasPermission('followup:visit:end', true)"
              class="fa fa-stop-circle-o stop-btn"
              aria-hidden="true"
              :title="$t('终止阶段')"
              @click="toStopPeriod(period)"
            />
          </span>
        </div>
        <div class="bottom-visit-list">
          <div
            v-for="(child, index) in period.visitList"
            :key="index"
            class="visit-item"
            :class="{ 'is-active': activeVisit == child }"
            @click="clickVisit(child)"
          >
            <div class="percent-circle">
              <hos-progress
                type="circle"
                :width="60"
                :show-text="false"
                :stroke-width="8"
                stroke-linecap="square"
                :percentage="getVisitStatus(child).circlePercent"
                :color="getVisitStatus(child).circleColor"
              />
              <span class="circle-text" :style="{ color: getVisitStatus(child).circleColor }">{{
                getVisitStatus(child).circleText
              }}</span>
              <span v-if="index !== period.visitList.length - 1" class="visit-line" />
            </div>
            <div class="right-content">
              <div class="right-top">
                <div class="visit-name-time">
                  <div style="width: 100%">
                    <div v-if="child.visitDate" class="name-edit">
                      <span class="name-span">{{ child.visitDate }}</span>
                      <hos-popover
                        placement="bottom"
                        popper-class="custom-visit-line-popper"
                        width="160"
                        trigger="hover"
                        :content="$t('修改计划随访时间')"
                      >
                        <i
                          v-if="!isPreview && $hasPermission('followup:visit:update', true)"
                          slot="reference"
                          class="hos-icon-edit"
                          @click.stop="toEditVisitPlanDate(child)"
                        />
                      </hos-popover>
                    </div>
                    <div class="name-edit">
                      <span class="name-span">{{ child.visitName }}</span>
                      <hos-popover
                        placement="bottom"
                        popper-class="custom-visit-line-popper"
                        width="100"
                        trigger="hover"
                        :content="$t('修改名称')"
                      >
                        <i
                          v-if="!isPreview && $hasPermission('followup:visit:update', true)"
                          slot="reference"
                          class="hos-icon-edit"
                          @click.stop="toAddFollowUp(false, child)"
                        />
                      </hos-popover>
                    </div>
                  </div>
                </div>
                <div v-if="!isPreview" class="handler-box">
                  <hos-popover v-if="subjectInfo.configJson.isEnableBindAdm === 1"
                    placement="bottom"
                    popper-class="custom-visit-line-popper"
                    width="100"
                    trigger="hover"
                    :content="$t('关联就诊')"
                  >
                    <i
                      v-if="$hasPermission('visit:adm:saveorupdate', true)"
                      slot="reference"
                      class="hos-icon-connection"
                      @click.stop="showVisitToggleDialog(child)"
                    />
                  </hos-popover>
                  <hos-popover
                    placement="bottom"
                    popper-class="custom-visit-line-popper"
                    width="100"
                    trigger="hover"
                    :content="$t('添加附件')"
                  >
                    <i
                      slot="reference"
                      class="hos-icon-folder-add"
                      @click.stop="attachmentDialogShow(child)"
                    />
                  </hos-popover>

                  <!-- 删,置为完成,改 任一一个展示时,才展示这个...更多按钮 -->
                  <div>
                    <hos-dropdown trigger="hover" size="small">
                      <span
                        v-if="
                          $hasPermission('followup:visit:update', true) ||
                          (child.status !== 2 && $hasPermission('followup:visit:update', true)) ||
                          (getDeleteBtnIsShow(child) && $hasPermission('followup:visit:delete', true))
                        "
                        style="cursor: pointer; font-size: 18px"
                        @click.stop=""
                      >
                        <i class="hos-icon-more" />
                      </span>
                      <hos-dropdown-menu slot="dropdown">
                        <hos-dropdown-item
                          v-if="$hasPermission('followup:visit:update', true)"
                          icon="hos-icom-add-item"
                          @click.native="openFormGroupDialog(period, child)"
                          >{{ $t('绑定组合套') }}</hos-dropdown-item
                        >
                        <hos-dropdown-item
                          v-if="child.status !== 2 && $hasPermission('followup:visit:update', true)"
                          icon="hos-icon-circle-check"
                          @click.native="toFinish(child)"
                          >{{ $t('随访置为完成') }}</hos-dropdown-item
                        >
                        <hos-dropdown-item
                          v-if="getDeleteBtnIsShow(child) && $hasPermission('followup:visit:delete', true)"
                          icon="hos-icon-delete"
                          @click.native="toDeleteVisit(child)"
                          >{{ $t('删除随访') }}</hos-dropdown-item
                        >
                      </hos-dropdown-menu>
                    </hos-dropdown>
                  </div>
                </div>
              </div>
              <div
                v-if="child.visitAdmRelationModel && child.visitAdmRelationModel.count > 0"
                class="right-bottom binded-adm-data"
                @click.stop="showVisitToggleDialog(child)"
              >
                <hos-tag v-if="getShowAdmElement(child).admType == 'O' || getShowAdmElement(child).admType == '门诊'" type="success" size="mini">{{
                  admTypes[getShowAdmElement(child).admType] ? admTypes[getShowAdmElement(child).admType] : getShowAdmElement(child).admType
                }}</hos-tag>
                <hos-tag v-else-if="getShowAdmElement(child).admType == 'I'|| getShowAdmElement(child).admType == '住院'" type="warning" size="mini">{{
                  admTypes[getShowAdmElement(child).admType] ? admTypes[getShowAdmElement(child).admType] : getShowAdmElement(child).admType
                }}</hos-tag>
                <hos-tag v-else-if="getShowAdmElement(child).admType == 'E'|| getShowAdmElement(child).admType == '急诊'" type="danger" size="mini">{{
                  admTypes[getShowAdmElement(child).admType] ? admTypes[getShowAdmElement(child).admType] : getShowAdmElement(child).admType
                }}</hos-tag>
                <hos-tag v-else type="info" size="mini">{{ admTypes[getShowAdmElement(child).admType] ? admTypes[getShowAdmElement(child).admType] : getShowAdmElement(child).admType }}</hos-tag>
                <!-- <span class="adm-time"></span> -->
                <span v-if="child.visitAdmRelationModel.count == 1" class="adm-dept single">
                  {{ getShowAdmElement(child).admTime ? getShowAdmElement(child).admTime.split(' ')[0] : '' }}
                  {{ getShowAdmElement(child).admDept ? getShowAdmElement(child).admDept : '' }}
                </span>

                <span v-else class="adm-dept multiple">
                  <span class="show-dept">
                    {{ getShowAdmElement(child).admTime ? getShowAdmElement(child).admTime.split(' ')[0] : '' }}
                    {{ getShowAdmElement(child).admDept ? getShowAdmElement(child).admDept : '' }}
                  </span>
                  <span v-if="child.visitAdmRelationModel.count > 1" class="more-info"
                    >等{{ child.visitAdmRelationModel.count }}次就诊</span
                  >
                </span>
              </div>
            </div>
          </div>
          <div
            v-if="
              period.frequency.hasNextVisitTime &&
              period.isNeedSetDate !== 1 &&
              period.periodStatus != 1 &&
              (period.unlimitedTotal || period.followTotal > period.visits.length) &&
              !isPreview
            "
            class="visit-handler"
          >
            <hos-button type="text" size="mini" @click="toSetNextVisitDate(period)">{{ $t('指定下次随访') }}</hos-button>
          </div>
          <div
            v-if="
              !isPreview &&
              period.periodStatus != 1 &&
              $hasPermission('followup:visit:save', true) &&
              period.isNeedSetDate !== 1 &&
              ((period.unlimitedTotal && !period.frequency.hasNextVisitTime) ||
                (period.followTotal > period.visits.length && !period.frequency.hasNextVisitTime))
            "
            class="visit-handler"
          >
            <hos-button type="text" size="mini" @click="toAddFollowUp(true, period)">{{ $t('新增随访') }}</hos-button>
          </div>
        </div>
      </div>
    </div>
    <follow-up-set-date ref="FollowUpSetDate" @confirm="followupSetDate" />
    <follow-up-add ref="FollowUpAdd" @confirm="followupAdd" />
    <form-group-dialog ref="FormGroupDialog" @confirm="formGroupConfirm" />
    <!-- 随访置为完成弹窗 -->
    <set-visit-finish v-if="visitFinishVisible" ref="setVisitFinish" @refreshDataList="refreshVisit" />
    <!-- 中止阶段并选择中止时间弹窗 -->
    <stop-visit v-if="visitStopVisible" :patientId="patientId" ref="stopVisit" @refreshDataList="refreshVisit" />
    <hos-dialog :title="$t('设置参考点时间')" append-to-body :visible.sync="referDialogVisible">
      <hos-form ref="referForm" :model="referForm">
        <hos-form-item :label="showRefrenceLable" prop="date" :rules="[{required:true,trigger:'blur',message:'参考点时间必填'}]">
          <hos-date-picker v-model="referForm.date" :placeholder="$t('请选择')" format="yyyy-MM-dd" value-format="yyyy-MM-dd" />
        </hos-form-item>
      </hos-form>
      <div slot="footer">
        <hos-button @click="referDialogVisible = false">{{ $t('取 消') }}</hos-button>
        <hos-button type="primary" @click="referFormConfirm">{{ $t('确 定') }}</hos-button>
      </div>
    </hos-dialog>

    <hos-dialog :title="$t('修改计划访视时间')" append-to-body :visible.sync="planDateDialogVisible">
      <hos-form ref="planDateForm" :model="planDateForm">
        <hos-form-item :label="$t('计划访视时间')" prop="newPlanDate" :rules="[{required:true,trigger:'blur',message:$t('计划访视时间')}]">
          <hos-date-picker v-model="planDateForm.newPlanDate" :placeholder="$t('请选择')" format="yyyy-MM-dd" value-format="yyyy-MM-dd" />
        </hos-form-item>
      </hos-form>
      <div slot="footer">
        <hos-button @click="planDateDialogVisible = false">{{ $t('取 消') }}</hos-button>
        <hos-button type="primary" @click="planDateFormConfirm">{{ $t('确 定') }}</hos-button>
      </div>
    </hos-dialog>

    <!-- 新的附件弹窗 -->
    <hos-biz-dialog :title="attachmentDialogTitle" width="70%" uid="patientViewAttachmentDialog" :close-on-click-modal="false">
    </hos-biz-dialog>
  </section>
</template>
<script>
import { debounce } from 'lodash'
import visitTimeLineMixin from './visit-time-line-mixin'
export default {
  name: 'TimeLine',
  mixins: [visitTimeLineMixin],
  components: {},
  data() {
    return {
      referDialogVisible: false,
      referForm: {
        date: ''
      },
      wholeBtnShow: true,
      showRefrenceLable: ''
    }
  },
  computed: {
  },
  watch: {},
  methods: {
    // 何时展示设置随访时间按钮
    refSetBtnShow(period) {
      if (!this.isPreview && period && period.isNeedSetDate === 1) {
        return true
      } else {
        return false
      }
    },
    // 查看当前时间轴
		seeCurrentLine() {
			this.$emit('toggleVisitLine', false)
			this.wholeBtnShow = true
		},
		// 查看完整时间轴
		seeWholeLine() {
			this.$emit('toggleVisitLine', true)
			this.wholeBtnShow = false
		},
    // 判断本阶段是否为以病种分类后的第一个阶段节点,因为第一个节点要加上病种或者主库名
		judgeFirstPeriodNode(period, allPeriods) {
			const diseaseId = period.diseaseId
			const filterArr = allPeriods.filter(ped => ped.diseaseId === diseaseId)
			const firstPeriodUid = filterArr[0].periodUid

			if (period.periodUid === firstPeriodUid) {
				return true
			} else {
				return false
			}
		},
		// 判断本阶段是否为以病种分类后的最一个阶段节点,最后一个节点才加上borde-bottom样式
		judgeLastPeriodNode(period, index, allPeriods) {
			const diseaseId = period.diseaseId
			const filterArr = allPeriods.filter(ped => ped.diseaseId === diseaseId)
			const length = filterArr.length

			const lastPeriodUid = filterArr[length - 1].periodUid
			if (period.periodUid === lastPeriodUid) {
				return true
			} else {
				return false
			}
		},
    referFormConfirm: debounce(function () {
      this.$refs.referForm.validate().then(() => {
        const origin_ExtJson = JSON.parse(this.patientInfo.extJson)
        // 添加参考点时间字段
        origin_ExtJson[this.curPeriodData.periodStartTime.type] = this.referForm.date
        const obj = {
          ...this.patientInfo,
          periodUid: this.curPeriodData.periodUid,
          extJson: JSON.stringify(origin_ExtJson)
        }
        this.$api('biz.edc.patient.patientView.setPeriodReferDate', obj).then((res) => {
          this.referDialogVisible = false
          this.$message.success(res.msg)
          this.$emit('refreshList')
        })
      }).catch(() => { })
    }, 1500, {
      'leading': true,
      'trailing': false
    })
  }
}
</script>
<style lang="scss" scoped>
.visit-time-line {
  width: 100%;
  height: 100%;
  padding: 5px;
	box-sizing: border-box;
  background-color: white;

  .period-list-container {
    width: 100%;
    height: 100%;
    position: relative;

    .whole-time-line-btn {
      width: 100%;
      padding: 8px 20px;
    }

    .period-item {
      margin-bottom: 10px;
      padding-bottom: 10px;

      // 全程阶段在按照病种分组后,要在外层套上border
			&.quancheng-period-item {
				position: relative;
				margin-bottom: 0;
				border-left: 1px solid #dcdfe6;
				border-right: 1px solid #dcdfe6;
			}

			&.first-period-node {
				margin-top: 15px;
				padding-top: 15px;
				border-top: 1px solid #dcdfe6;
				border-top-left-radius: 5px;
				border-top-right-radius: 5px;
			}

			&.last-period-node {
				border-bottom: 1px solid #dcdfe6;
				border-bottom-left-radius: 5px;
				border-bottom-right-radius: 5px;
				margin-bottom: 10px;
			}

      .period-disease-name {
        position: absolute;
        top: -10px;
        left: 10px;
        background-color: white;
        font-size: 14px;
      }

      .top-period-info {
        display: flex;
        justify-content: space-between;
        padding: 0 5px 5px 10px;

        .period-name {
          width: 270px;
          font-size: 16px;
          font-weight: normal;
          text-align: left;
          color: #daa755;
          font-weight: 600;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .set-refer-date {
            position: relative;
            top: -2px;
            margin-left: 10px;
            color: #009dff;
            font-size: 14px;
            cursor: pointer;
          }
        }

        .stop-btn {
          color: #f56c6c;
          cursor: pointer;
          font-size: 18px;
          margin-top: 5px;
        }
      }

      .bottom-visit-list {
        .visit-item {
          display: flex;
          align-items: center;
          height: 78px;
          padding-left: 10px;

          .percent-circle {
            width: 60px;
            height: 60px;
            cursor: pointer;
            position: relative;

            .circle-text {
              width: 60px;
              height: 60px;
              text-align: center;
              line-height: 60px;
              left: 0;
              position: absolute;
              font-size: 10px;
              font-weight: 800;
            }
            .visit-line {
              position: absolute;
              width: 3px;
              height: 20px;
              border-right: 3px dashed #c1c1c1;
              bottom: -20px;
              left: 25px;
            }
          }

          .right-content {
            width: calc(100% - 60px);
            height: 100%;
            display: flex;
            flex-direction: column;
            .right-top {
              display: flex;
              flex-grow: 1;

              .visit-name-time {
                width: calc(100% - 90px);
                height: 100%;
                margin-left: 8px;
                display: flex;
                align-items: center;
                cursor: pointer;
                position: relative;
                .time {
                  font-size: 14px;
                  color: #606266;
                }
                .name-edit {
                  font-size: 16px;
                  color: #606266;
                  position: relative;

                  .name-span {
                    display: inline-block;
                    max-width: 80%;
                    margin-right: 5px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-size: 14px;
                  }

                  i {
                    cursor: pointer;
                    position: relative;
                    top: -2px;
                  }
                }
              }

              .handler-box {
                width: 100px;
                padding-left: 10px;
                padding-right: 10px;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                i {
                  font-size: 20px;
                  color: #009dff;
                  cursor: pointer;
                  margin-right: 10px;
                }
              }
            }

            .right-bottom {
              width: 100%;
              height: 18px;
              color: #606266;
              font-size: 12px;
              position: relative;
              top: -8px;
              padding-left: 3px;
              display: flex;
              align-items: center;
              cursor: pointer;

              .hos-tag {
                width: 30px;
                height: 16px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 10px;
                margin-right: 2px;
              }
              .adm-time {
                width: 64px;
                display: inline-block;
                margin-left: 2px;
                margin-right: 5px;
              }

              .adm-dept {
                width: calc(100% - 32px);
                display: inline-block;
                color: #999;
              }

              .single {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;
              }

              .multiple {
                display: flex;
                .show-dept {
                  width: calc(100% - 60px);
                  text-overflow: ellipsis;
                  overflow: hidden;
                  word-break: break-all;
                  white-space: nowrap;
                }

                .more-info {
                  width: 70px;
                }
              }
            }
          }
        }

        .visit-item.is-active {
          background-color: #c6e2ff;
        }

        .visit-handler {
          // height: 24px;
          padding-left: 10px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.custom-visit-line-popper {
  min-width: 100px;
  padding: 5px 10px;
  text-align: center;
}
</style>