<!-- 访视进度单元 -->
<template>
    <div class="visit-progress-cell">
        <hos-popover v-for="visit in visits" :key="visit.id" placement="bottom" width="330" trigger="hover"
            :visible-arrow="false" popper-class="custom-tips-popover">
            <div class="popover-content">
                <div>{{ $t('随访名称') }}：{{ visit.name }}</div>
                <div>{{ $t('随访状态') }}：{{ statusMap[visit.visitWindowsStatus] ? statusMap[visit.visitWindowsStatus] : '-' }}</div>
                <div>{{ $t('计划随访日期') }}：{{ visit.planDate ? visit.planDate : '-' }}</div>
                <div>
                    {{ $t('随访窗口时间') }}：{{ visit.startDate ? visit.startDate : '' }}{{ visit.startDate ? $t('至') :
                    '-' }}{{ visit.endDate ? visit.endDate : '' }}
                </div>
                <div>{{ $t('实际随访日期') }}：{{ visit.realTime ? visit.realTime : '-' }}</div>
            </div>
            <div slot="reference" class="visit-status-popover">
                <span class="visit-cell" style="color:#fff;" :class="[statusClass[visit.visitWindowsStatus]]" :title="$t('点击查看详情')"
                    @click="clickVisit(visit)">
                    {{ visit.expiredDay || '' }}
                </span>
            </div>
        </hos-popover>
        <hos-biz-dialog width="70%" title="访视信息-表单" uid="VisitFormDialog" :close-on-click-modal="false" />
    </div>
</template>

<script>
export default {
    name: 'VisitProgressCell',
    props: {
        row: Object,
        column: Object
    },
    data() {
        return {
            statusMap: {
                1: this.$t('已随访'),
                2: this.$t('窗口内待随访'),
                3: this.$t('超期未随访'),
                4: this.$t('窗口外')
            },
            statusClass: {
                1: 'success',   // 已随访
                2: 'primary',   // 窗口内待随访
                3: 'danger',    // 超期未随访
                4: 'info'       // 窗口外
            }
        }
    },
    computed: {
        visits() {
            return this.row.visitList.filter(item => item.uid === this.column.property)
        }
    },
    methods: {
        clickVisit(visit) {
            this.$emit('clickVisit',visit)
        }
    }
}
</script>

<style lang="scss" scoped>
.visit-progress-cell {
    display: flex;
    height: 16px;
    line-height: 16px;
    color: #fff;

    .visit-cell {
        width: 30px;
        height: 16px;
        padding: 0 10px;
        margin: 0 5px;
        text-align: center;
    }
}


.success {
    background-color: #5db42f;
}

.primary {
    background-color: #409eff;
}

.warning {
    background-color: #EC9D00;
}

.danger {
    background-color: #F25757;
}

.info {
    background-color: #a1a1a1;
}
</style>