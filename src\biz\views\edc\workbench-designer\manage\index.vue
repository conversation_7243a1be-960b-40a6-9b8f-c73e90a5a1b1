<!--
  * @FileDescription：数据概览设计管理（仪表盘）
-->
<template>
    <div class="h-fit">
        <biz-crud ref="listPage" v-loading="isUpdating" :option="crudOption" uid="workbenchTable">
            <template #buttonLeftOnTable>
                <hos-button icon="hos-icom-add" @click="openAddDialog()">{{ $t('新增') }}</hos-button>
                <hos-button icon="hos-icom-cancel" @click="$refs.listPage.deletion()">{{ $t('删除') }}</hos-button>
                <hos-button icon="hos-icom-import" @click="importHandle()">{{ $t('导入') }}</hos-button>
                <hos-button icon="hos-icom-export" @click="exportDashboard()">{{ $t('导出') }}</hos-button>
            </template>
            <template #enableFlag="{ row }">
                <hos-switch v-model="row.enableFlag" :active-value="1" :inactive-value="0"
                    @change="(val) => changeStatus(row, val)" />
            </template>
            <template #business="{ row }">
                <span v-if="row.business == 1">数据探索</span>
                <span v-if="row.business == 2">EDC全局</span>
                <span v-if="row.business == 3">整个系统(不包括项目内)</span>
                <span v-else>仅项目内</span>
            </template>
            <template #operation="{ row }">
                <hos-tooltip class="pl5 pr5" :content="$t('设计')">
                    <i class="hos-icom-design" :class="{ disabled: row.enableFlag != 0 }"
                        @click.stop="goToDesign(row)"></i>
                </hos-tooltip>
                <hos-tooltip class="pl5 pr5" :content="$t('预览')">
                    <i class="hos-icom-eye" @click.stop="preview(row)"></i>
                </hos-tooltip>
                <hos-tooltip class="pl5 pr5" :content="$t('复制链接')">
                    <i class="hos-icom-copy" @click.stop="copyLink(row)"></i>
                </hos-tooltip>
                <hos-tooltip class="pl5 pr5" :content="$t('编辑')">
                    <i class="hos-icon-edit" @click="openEditDialog(row)"></i>
                </hos-tooltip>
                <hos-tooltip class="pl5 pr5" :content="$t('删除')">
                    <i class="hos-icom-cancel" @click="$refs.listPage.deleteRow(row)"></i>
                </hos-tooltip>
            </template>
        </biz-crud>
        <!-- 弹窗, 新增 / 修改 -->
        <hos-biz-dialog :title="dialogTitle" width="40%" uid="WorkbenchAddDialog" :close-on-click-modal="false" />
        <!-- 大屏导入 -->
        <report-upload ref="upload" :v-if="uploadVisible" @afterUpload="afterUpload"></report-upload>
    </div>
</template>

<script>
import axios from 'axios'
import Vue from 'vue'
import { getCurrentLocale } from '@base/utils/i18n/i18n-util'
import UserConstant from '@base/constant/user-constant'
import reportUpload from './report-upload.vue'
import { getToken } from '@base/utils/base/token-util'
export default {
	components: { reportUpload },
    data() {
        return {
            uploadVisible:false,
            dataForm: {
                reportName: ''
            },
            isUpdating: false,
            dialogTitle: '',
            businessOption: [
                { label: '数据探索', value: 1 },
                { label: 'EDC全局', value: 2 },
                { label: '整个系统(不包括项目内)', value: 3 },
                { label: '仅项目内', value: 4 },
            ],
            enableOption: [
                { label: '是', value: 1 },
                { label: '否', value: 0 },
            ]
        }
    },
    computed: {
        crudOption() {
            return {
                queryFormFields: [
                    {
                        inputType: 'input',
                        label: this.$t('概览名称'),
                        field: 'instrumentBoardName',
                    },
                    {
                        inputType: 'input',
                        label: this.$t('概览描述'),
                        field: 'instrumentBoardDesc',
                    },
                    {
                        inputType: 'select',
                        label: this.$t('统计业务数据范围'),
                        field: 'business',
                        SelectOption: {
                            localOptions: this.businessOption,
                            label: 'label',
                            option: 'value'
                        }
                    },
                    {
                        inputType: 'select',
                        label: this.$t('是否启用'),
                        field: 'enableFlag',
                        SelectOption: {
                            localOptions: this.enableOption,
                            label: 'label',
                            option: 'value'
                        }
                    },
                ],
                buttons: {
                    query: {
                        api: (p) => {
                            return this.$api('biz.edc.instrumentBoard.pageListApi', p)
                        }
                    },
                    delete: {
                        api: (p) => {
                            return this.$api('biz.edc.instrumentBoard.deleteApi', p)
                        },
                        isShow: false
                    }
                },
                columns: [
                    {
                        type: 'selection',
                        width: '50px',
                        align: 'center'
                    },
                    {
                        label: this.$t('概览名称'),
                        field: 'instrumentBoardName',
                    },
                    {
                        label: this.$t('概览ID'),
                        field: 'id',
                    },
                    {
                        label: this.$t('概览描述'),
                        field: 'instrumentBoardDesc',
                    },
                    {
                        label: this.$t('统计业务数据范围'),
                        field: 'business',
                        slotName: 'business'
                    },
                    {
                        label: this.$t('是否启用'),
                        field: 'enableFlag',
                        slotName: 'enableFlag'
                    },
                    {
                        label: this.$t('common.operation'),
                        width: '200px',
                        prop: 'operation',
                        slotName: 'operation',
                        fixed: 'right'
                    }
                ]
            }
        }
    },
    methods: {
        importHandle() {
            this.uploadVisible = true
            this.$nextTick(() => {
                this.$refs.upload.init()
            })
        },
        afterUpload() {
            this.uploadVisible = false
            this.refreshThePage()
        },
        BlobDownLoad(res, type) {
            let blob = new Blob([res.data], {
                type: type
            })
            const fileName = decodeURIComponent(res.headers['content-disposition'].split('filename=')[1])
            const href = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.style.display = 'none'
            a.href = href
            a.download = fileName + '.zip'
            a.click()
            a.remove()
            URL.revokeObjectURL(a.href)
        },
        exportDashboard() {
            const ids = this.$refs['listPage'].checkRecords.map(i => i.id)
            if(!ids || ids.length == 0) {
                return
            }
            axios({
                url: `${this.$baseUrl}/edc/instrument-board/batch/export`,
                method: 'post',
                responseType: 'blob',
                headers: {
                    'access-token': getToken(),
                    'client-ip': Vue.ls.get( UserConstant.IP ),
                    'client-mac': Vue.ls.get( UserConstant.Mac ),
                    language: getCurrentLocale()
                },
                data: ids
            } ).then(
                async ( response ) => {
                    if ( response.data.size ) {
                        this.BlobDownLoad( response,'application/octet-stream' )
                    } else {
                        this.$message( {
                            message: this.$t( '未获取到文件' ),
                            type: 'error'
                        } )
                    }
                },
                ( error ) => {
                    console.log( error )
                }
            )
        },
        refreshThePage() {
            this.$store.commit('UPDATE_TABLE', {
                _uid: 'workbenchTable'
            })
        },
        openAddDialog() {
            this.dialogTitle = this.$t('新增')
            this.$store.commit('OPEN_DIALOG', {
                component: require('./add-or-update.vue').default,
                _uid: 'WorkbenchAddDialog',
                props: {
                    id: '',
                    status: 'add',
                    row: {},
                }
            })
        },
        openEditDialog(row) {
            this.dialogTitle = this.$t('修改')
            this.$store.commit('OPEN_DIALOG', {
                component: require('./add-or-update.vue').default,
                _uid: 'WorkbenchAddDialog',
                props: {
                    id: row.id,
                    status: 'edit',
                    row: row,
                }
            })
        },
        changeStatus(row, val) {
            this.$api('biz.edc.instrumentBoard.editApi', row).then(res => {
                this.$message({
                    message: this.$t('biz.edc.prompt.success'),
                    type: 'success',
                    duration: 500,
                    onClose: () => {
                        this.refreshThePage()
                    }
                })
            }).catch(() => { })
        },
        goToDesign(row) {
            if(row.enableFlag != 0){
                return
            }
            this.$router.push({
                path: '/workbench-designer-tab/designer',
                query: {
                    id: row.id,
                    name: row.instrumentBoardName
                }
            })
        },
        copyLink(row) {
            const url = `/edc/workbench-designer/page-viewer?id=${row.id}`
            this.$copyText(url).then((e) => {
                // success
                this.$message({
                    message: '数据概览链接已复制到剪切板，可在菜单管理页面新增菜单，将路由设置为剪贴板的内容',
                    type: 'success'
                })
            }, (e) => {
                // fail
                this.$message.error('数据概览链接复制失败。')
            })
        },
        preview(row) {
            this.$router.push({
                path: '/workbench-designer-tab/viewer',
                query: {
                    id: row.id,
                    name: row.instrumentBoardName
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
  .hos-icom-design.disabled{
    cursor: not-allowed;
    color: #aaa;
  }
</style>