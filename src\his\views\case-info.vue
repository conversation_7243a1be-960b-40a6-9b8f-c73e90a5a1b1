<!--
  * @FileDescription：患者入组
-->
<template>
  <div v-loading="loading" class="case-content" :element-loading-text="loadingText">
    <hos-form
      ref="dataForm"
      :model="dataForm"
      label-width="120px"
      size="large"
      label-position="right"
      style="margin-right: 30px"
      :disabled="formReadonly"
    >
      <template v-for="(item, index) in baseData">
        <hos-form-item
          v-if="item.code == 'regno'"
          :key="index"
          :prop="item.code"
          :label="item.label"
          :rules="
            item.required ? [{ required: true, message: item.label + '为必填项', trigger: ['blur', 'change'] }] : []
          "
        >
          <hos-input v-model="dataForm[item.code]" :disabled="!!dataForm.id" :placeholder="item.label" />
        </hos-form-item>
        <!-- 姓名 -->
        <hos-form-item
          v-else-if="item.code == 'name'"
          :key="index"
          :prop="item.code"
          :label="item.label"
          :rules="[{ required: true, message: item.label + '为必填项', trigger: ['blur', 'change'] }]"
        >
          <hos-input
            v-model="dataForm[item.code]"
            auto-complete="off"
            maxlength="20"
            :placeholder="item.label"
            show-word-limit
          />
        </hos-form-item>
        <!-- 姓名缩写 -->
        <hos-form-item v-else-if="item.code == 'initials'" :key="index" prop="initials" :label="item.label">
          <hos-input v-model="dataForm['initials']" :placeholder="item.label" maxlength="20" show-word-limit disabled />
        </hos-form-item>
        <!-- 对身份验证 -->
        <hos-form-item
          v-else-if="item.code == 'idCard'"
          :key="index"
          :prop="item.code"
          :label="item.label"
          class="role-list"
          :rules="[
            { required: item.required, message: item.label + '为必填项', trigger: 'blur' },
            {
              validator: 'regexp',
              pattern: /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
              message: '请输入正确的身份证号码',
              trigger: 'blur'
            }
          ]"
        >
          <hos-input v-model="dataForm[item.code]" :placeholder="item.label" @blur="idCardBlur()" />
        </hos-form-item>
        <hos-form-item
          v-else-if="item.code == 'gender'"
          :key="index"
          :prop="item.code"
          :label="item.label"
          class="role-list"
          :rules="[{ required: true, message: item.label + '为必填项', trigger: ['blur', 'change'] }]"
        >
          <hos-select v-model="dataForm[item.code]" :placeholder="item.label" style="width: 100%">
            <hos-option v-for="i in genderList" :key="i.value" :label="i.label" :value="+i.value" />
          </hos-select>
        </hos-form-item>
        <hos-form-item
          v-else-if="item.code == 'birthday'"
          :key="index"
          :prop="item.code"
          :label="item.label"
          class="role-list"
          :rules="[{ required: true, message: item.label + '为必填项', trigger: ['blur', 'change'] }]"
        >
          <hos-date-picker
            v-model="dataForm[item.code]"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :placeholder="item.label"
            style="width: 100%"
            :picker-options="option"
          />
        </hos-form-item>
        <hos-form-item
          v-else-if="item.code == 'intoDate'"
          :key="index"
          :prop="item.code"
          :label="item.label"
          class="role-list"
          :rules="[{ required: true, message: item.label + '为必填项', trigger: ['blur', 'change'] }]"
        >
          <hos-date-picker
            v-model="dataForm[item.code]"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :placeholder="item.label"
            style="width: 100%"
            :picker-options="option"
          />
        </hos-form-item>
        <!-- 中心唯一字段/系统唯一字段/项目唯一字段/病案号 -->
        <hos-form-item
          v-else-if="
            item.code == 'filterNo' ||
            item.code == 'sysUniqueId' ||
            item.code == 'projUniqueId' ||
            item.code == 'recordId'
          "
          :key="index"
          :prop="item.code"
          :label="item.label"
          class="role-list"
          :rules="
            item.required ? [{ required: true, message: item.label + '为必填项', trigger: ['blur', 'change'] }] : []
          "
        >
          <hos-input v-model="dataForm[item.code]" :placeholder="item.label" />
        </hos-form-item>
        <!-- 手机号 -->
        <hos-form-item
          v-else-if="item.code == 'tel'"
          :key="index"
          :prop="item.code"
          :label="item.label"
          :rules="[
            { required: item.required, message: item.label + '为必填项', trigger: ['blur', 'change'] },
            { validator: 'regexp', pattern: /^1[0-9]{10}$/, message: '手机号码格式不正确', trigger: 'blur' }
          ]"
        >
          <hos-input v-model="dataForm[item.code]" auto-complete="off" :placeholder="item.label" show-word-limit />
        </hos-form-item>
        <hos-form-item v-else-if="item.code == 'groupIds'" :key="index" :prop="item.code" :label="item.label"
          class="role-list"
          :rules="[{ required: item.required, message: item.label + '为必填项', trigger: ['blur', 'change'] }]">
          <hos-select v-model="dataForm[item.code]" clearable :placeholder="item.label" style="width: 100%">
            <hos-option v-for="i in groupList" :key="i.id" :label="i.name" :value="i.id" />
          </hos-select>
        </hos-form-item>
        <hos-form-item v-else-if="item.code == 'labelIds'" :key="index" :prop="item.code" :label="item.label"
          class="role-list"
          :rules="[{ required: item.required, message: item.label + '为必填项', trigger: ['blur', 'change'] }]">
          <hos-select v-model="dataForm[item.code]" multiple :placeholder="item.label" style="width: 100%">
            <hos-option v-for="i in tagList" :key="i.id" :label="i.name" :value="i.id" />
          </hos-select>
        </hos-form-item>
        <hos-form-item v-else-if="item.code == 'diseaseId'" :key="index" :prop="item.code" :label="item.label"
          class="role-list"
          :rules="[{ required: item.required, message: item.label + '为必填项', trigger: ['blur', 'change'] }]">
          <hos-select v-model="dataForm[item.code]" clearable :placeholder="item.label" style="width: 100%">
            <hos-option v-for="i in diseaseList" :key="i.id" :label="i.name" :value="i.id" />
          </hos-select>
        </hos-form-item>
        <!-- 基础字段code不能匹配时 -->
        <hos-form-item v-else :key="index" :prop="item.code" :label="item.label"
          :rules="[{ required: item.required, message: item.label + '为必填项', trigger: ['blur', 'change'] }]">
          <hos-input v-model="dataForm[item.code]" :placeholder="item.label" />
        </hos-form-item>
      </template>
    </hos-form>
    <hos-row style="margin-right: 30px">
      <case-expand-form ref="ExpandProps" :meta-data="metaData" :gender-list="genderList" :getExternalFieldList="getExternalFieldList"
        :external-result="externalResult" :is-center-case="isCenterCase" />
    </hos-row>
  </div>
</template>

<script>
import CaseExpandForm from '@/views/components/patient-add-or-update/case-expand-form'
import { debounce } from 'lodash'
import { basicInfoFieldList, basicInfoFieldObj } from '@/utils/enum'
import { pinyin } from '@/utils/Convert_Pinyin'
import { formateDate } from '@/utils/date'
export default {
  components: {
    CaseExpandForm
  },
  props: {
    formReadonly: {
      type: Boolean,
      default: false
    },
    doctorId: {
      type: null,
      default: ''
    },
    patientId: {
      type: null,
      default: ''
    },
    projectId: [Number, String],
    subjectId: [Number, String],
    orgId: [Number, String],
    foreignId: [Number, String],
    patientInfo:{
      type:Object,
      default(){
        return {}
      }
    }
  },
  data() {
    return {
      extFields:['ext1','ext2','ext3','momRegno'],
      tabPosition: 'left',
      dataForm: {
        id: null,
        isHisCase: 1,
        regno: '',
        name: '',
        initials: '',
        idCard: '',
        gender: '',
        birthday: '',
        filterNo: '',
        sourceDeptId: this.orgId,
        intoDate: formateDate()
      },
      loading: false,
      loadingText: '正在获取表单信息，请等待...',
      genderList: [
        {label:'男',value:1},
        {label:'女',value:2},
        {label:'不确定',value:3},
      ],
      isHisCaseEnum: [],
      centerName: '',
      centerType: '',
      option: {
        disabledDate: (time) => {
          return time.getTime() > Date.now() - 1 * 24
        }
      },
      showExpandTips: false,
      showExpandForm: false,
      originalMetaData: [], // 包括基本字段在内的数组
      baseData: [],
      metaData: [], // 过滤基本字段后的数组，避免基本字段在拓展表单中重复展示
      externalResult: [],
      // 是否允许从外部接口获取数据 0表示不允许
      isCenterCase: 0,
      groupList:[],
      diseaseList: [],
      tagList:[]
    }
  },
  computed: {
    expandTips() {
      return this.showExpandForm ? '收起' : '点击展开患者拓展信息'
    }
  },
  watch: {
    'dataForm.name'(newV, oldV) {
      if (newV) {
        this.getInitial(newV)
      }
    },
    // subjectId: {
    //   handler(newV, oldV) {
    //     // 切换项目后重新渲染
    //     this.init()
    //   },
    //   immediate: true
    // }
  },
  async created() {
    this.getDictItem()
  },
  methods: {
    init(res){
      this.getCaseExpandField().then(() => {
          // 重新获取患者信息
          if (this.$refs['dataForm']) {
            this.$refs['dataForm'].resetFields()
          }
          if(res){
            res.extJson = res.extJson ? JSON.parse(res.extJson) : {}
            // 将ext类字段视作自定义字段，编辑时在extJson中编辑，保存时保存到外层form对象中
            this.extFields.forEach(key=>{
              if(res[key] !== undefined){
                res.extJson[key] = res[key]
                delete res[key]
              }
            })
            if(res.labelIds){
              res.labelIds = res.labelIds.split(',')
            }
            this.dataForm = {
              ...this.dataForm,
              ...res,
              intoDate: res.intoDate ? res.intoDate : formateDate()
            }
            this.$refs['ExpandProps'].formModel = res.extJson
            // 保存拓展字段的初始值,随访参考点要更根据初始值的有无判断是否可以修改
            this.$refs['ExpandProps'].initFormValue = this.deepClone(res.extJson || {})
            this.getExternalInfo(this.dataForm.regno)
          }else{
            this.getInfo()
          }
        })
    },
    // 根据登记号获取信息
    getInfo() {
      // 当患者是his患者时，调用his接口，获取患者信息，回显
      this.$axios
        .get(`/openApi/patient/get-patient-info`, {
          params: {
            regno: this.patientId
          }
        })
        .then(({data:res}) => {
          if (res && res.name) {
            if(res.labelIds){
              res.labelIds = res.labelIds.split(',')
            }
            res.extJson = res.extJson ? JSON.parse(res.extJson) : {}
            // 将ext类字段视作自定义字段，编辑时在extJson中编辑，保存时保存到外层form对象中
            this.extFields.forEach(key=>{
              if(res[key] !== undefined){
                res.extJson[key] = res[key]
                delete res[key]
              }
            })
            this.dataForm = {
              ...this.dataForm,
              ...res,
              intoDate: res.intoDate ? res.intoDate : formateDate()
            }
            this.$refs['ExpandProps'].formModel = res.extJson
            // 保存拓展字段的初始值,随访参考点要更根据初始值的有无判断是否可以修改
            this.$refs['ExpandProps'].initFormValue = this.deepClone(res.extJson || {})
          } else {
            // 如果填写了错误或者不存在的登记号导致没有拉取到his信息，不回显，当作新患者填写
            this.$refs.dataForm.resetFields()

            // 没有his入组过自动填充url中的登记号
            this.dataForm.regno = this.patientId
          }
          this.getExternalInfo(this.dataForm.regno)
        })
        .catch(() => {})

    },
    async getExternalFieldList(categoryId){
      const res = await this.$axios.get(`/openApi/external/data/external-item-prop-list?categoryId=${categoryId}`)
      return res.data || []
    },
    // 获取外部接口信息
    getExternalInfo(pin) {
      // pin = 'P9867172500'
      // 如果本项目包含拓展字段并且拓展字段中有字段配置了外部接口，调接口获取该字段的值
      const temArr = []
      this.metaData.map((item) => {
        if (item.externaldataInfo && item.externaldataInfo.CatID) {
          item.ID = item.code
          item.externaldataInfo.ID = item.code
          temArr.push(item.externaldataInfo)
        }
      })
      // console.log('temArr',temArr,'isCenterCase',this.isCenterCase,'pin',pin)
      if (temArr.length > 0 && this.isCenterCase && pin) {
        this.$axios.post('/openApi/external/data/get-crf-exdata-intogroup',{
            itemStr: JSON.stringify(temArr),
            regno: pin,
            admIds: '=' // 关联的就诊id??有这个概念吗??
          }).then(({data:res}) => {
            console.log(res, 'externaldataInfo')
            // FIXME:接口掉通过后将外部接口字段信息显示到表单中
            if (res && typeof res === 'string') {
              this.externalResult = JSON.parse(res)
            } else {
              this.externalResult = res
            }
            // this.externalResult = [
            //   {
            //       "DataObject": [
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           }
            //       ],
            //       "Message": "tuo",
            //       "Result": true
            //   }
            // ]

            // 让子组件回显设置了外部接口的字段
            this.$nextTick(() => {
              this.$refs.ExpandProps.setExternalResult()
            })
          })
      }
    },
    getInitial(name) {
      this.dataForm.initials = pinyin.getCamelChars(name)
    },
    // 表单提交,这里有双重表单验证
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          if (!this.subjectId) {
            return
          }
          // 患者拓展信息验证
          this.$refs['ExpandProps'].$refs['form'].validate((valid2) => {
            if (!valid2) {
              this.$message({
                message: '请填写患者拓展信息',
                type: 'warning',
                duration: 1000
              })
              if (!this.showExpandForm) {
                this.showExpandFormHandle()
              }
              return false
            } else {
              // 获取拓展信息表单的填写内容
              const expandFormValue = JSON.parse(JSON.stringify(this.$refs['ExpandProps'].formModel))
              const dataForm = JSON.parse(JSON.stringify(this.dataForm))
              if(dataForm.labelIds && Array.isArray(dataForm.labelIds)){
                dataForm.labelIds = dataForm.labelIds.join(',')
              }
              this.extFields.forEach(key=>{
                if(expandFormValue[key] !== undefined){
                  dataForm[key] = expandFormValue[key]
                  delete expandFormValue[key]
                }
              })
              this.loading = true
              this.loadingText = '正在保存，请等待...'
              this.$axios.post(
                '/openApi/patient/his-into-patient',
                {
                  ...dataForm,
                  projectId: this.projectId,
                  subjectId: this.subjectId,
                  extJson: JSON.stringify(this.getExpandField(expandFormValue)),
                  orgId:this.orgId,
                  intoUserId: this.doctorId
                },
                { params: {} }
              )
                .then(({data,msg}) => {
                  // this.$refs['dataForm'].resetFields()
                  // // 重置拓展表单
                  // this.$refs['ExpandProps'].$refs['form'].resetFields()
                  this.$message({
                    message: msg,
                    type: 'success',
                    duration: 500,
                  })

                  // 入组后查询患者在内部系统的信息
                  this.$emit('afterHisInGroup')
                })
                .finally(() => {
                  this.loading = false
                })
            }
          })
        })
      },
      1000,
      {
        leading: true,
        trailing: false
      }
    ),
    // his调用专门的接口获取字典项
    getDictItem() {
      // this.$axios
      //   .get(`/sys/dict/select-usable-pcode`, {
      //     params: {
      //       code: 'gender',
      //     }
      //   })
      //   .then(({data:res}) => {
      //     if(res && res.length>0){
      //       this.genderList = res
      //     }
      //   })
    },
    // 输入身份证号失焦后根据校验校验结果与日期选择器联动
    idCardBlur() {
      const pattern = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
      // 校验未通过时，return
      if (!pattern.test(this.dataForm.idCard)) {
        return
      }
      // 校验通过，联动日期选择器
      let birthStr = this.dataForm.idCard.slice(6, 14)
      birthStr = `${birthStr.slice(0, 4)}-${birthStr.slice(4, 6)}-${birthStr.slice(6)}`
      this.dataForm.birthday = birthStr
    },
    getExtension(nameStr) {
      const arr = nameStr.split('.')
      const len = arr.length
      return arr[len - 1]
    },
    getExpandField(obj) {
      basicInfoFieldList.forEach((item) => {
        delete obj[item]
      })
      return obj
    },
    // 获取项目所有的拓展患者拓展字段
    getCaseExpandField() {
      if (!this.subjectId || !this.orgId) {
        return new Promise(() => {})
      }
      let curUrl = ''
      this.loading = true
      this.loadingText = '正在获取表单信息，请等待...'
      curUrl = `/openApi/project/field/${this.subjectId}/${this.orgId}`

      return this.$axios({
        method: 'get',
        url: curUrl,
        params: {}
      }).then(({data:res}) => {
        this.isCenterCase = res.isCenterCase
        res = res.fields || []
        // 拓展字段中有必填项才需要给出提示，并填写
        if (res && res.length > 0) {
          this.showExpandTips = true

          const curBaseData = []
          const curMetaData = []
          res.map((item, index) => {
            // if (item.code == 'name' || item.code == 'gender' || item.code == 'birthday' || item.code == 'filterNo' || item.code == 'intoDate' || item.isShow) {
            if (item.isShow == null || item.isShow) {
              if (item.isBaseField && !this.isExtField(item.code)) {
                curBaseData.push({
                  id: item.id,
                  // key: item.widgetType + '_' + index,
                  label: item.name,
                  code: item.code,
                  required: item.isRequired === 1,
                  type: item.widgetType ? item.widgetType : this.getType(item.code),
                  isShow: 1,
                  sort: item.listSort,
                  widgetOption: item.widgetOption ? item.widgetOption : '',
                  externaldataInfo: item.externalDataInfo ? JSON.parse(item.externalDataInfo) : ''
                })
              } else {
                curMetaData.push({
                  id: item.id,
                  // key: item.widgetType + '_' + index,
                  label: item.name,
                  code: item.code,
                  required: item.isRequired === 1,
                  type: item.widgetType ? item.widgetType : this.getType(item.code),
                  isShow: 1,
                  sort: item.listSort,
                  widgetOption: item.widgetOption ? item.widgetOption : '',
                  externaldataInfo: item.externalDataInfo ? JSON.parse(item.externalDataInfo) : ''
                })
              }
            }
          })
          // this.originalMetaData = temArr
          // 过滤是为了避免再到拓展字段表单组件中去修改加判断条件
          // 基础字段
          // const curBaseData = this.originalMetaData.filter(item => basicInfoFieldList.includes(item.code))
          this.baseData = curBaseData.sort(function (a, b) {
            if (a.sort < b.sort) {
              return -1
            } else if (a.sort === b.sort) {
              return 0
            } else {
              return 1
            }
          })
          // 扩展字段
          // const curMetaData = this.originalMetaData.filter(item => !basicInfoFieldList.includes(item.code))
          this.metaData = curMetaData.sort(function (a, b) {
            if (a.sort < b.sort) {
              return -1
            } else if (a.sort === b.sort) {
              return 0
            } else {
              return 1
            }
          })
          console.log(this.baseData, 'baseData')
          console.log(this.metaData, 'metaData')
          if(this.baseData.filter(item=>item.code == 'groupIds').length>0){
            this.$axios({method: 'get',url: '/openApi/project/edc/patient-group/list-by-subject',params: {subjectId:this.subjectId}}).then(({data:res}) => {
              this.groupList = res && res.length>0 ? res : []
            })
          }else{
            this.groupList = []
          }
          if(this.baseData.filter(item=>item.code == 'labelIds').length>0){
            this.$axios({method: 'get',url: '/openApi/project/edc/patient-label/list-by-subject',params: {subjectId:this.subjectId}}).then(({data:res}) => {
              this.tagList = res && res.length>0 ? res : []
            })
          }else{
            this.tagList = []
          }
          if(this.baseData.filter(item=>item.code == 'diseaseId').length>0){
            this.$axios({method: 'get',url: '/openApi/project/edc/subject-disease/list',params: {subjectId:this.subjectId}}).then(({data:res}) => {
              this.diseaseList = res && res.length>0 ? res : []
            })
          }else{
            this.diseaseList = []
          }
        }
      }).finally(()=>{
        this.loading = false
      })
    },
    isExtField(code){
        return this.extFields.indexOf(code) > -1
    },
    // 展示拓展字段表单
    showExpandFormHandle() {
      this.showExpandForm = !this.showExpandForm
    },
    getType(code) {
      return basicInfoFieldObj[code]
    },
  }
}
</script>

<style lang="scss" scoped>
.case-container {
  margin-top: 60px;

  .case-radio {
    width: 100%;
    text-align: center;
    margin-bottom: 30px;
  }

  .case-content {
    .single-form-container {
      width: 550px;
      margin: 0 auto;

      @media (min-width: 768px) {
        width: 100%;
      }

      @media (min-width: 1024px) {
        width: 550px;
      }

      .button-row {
        padding: 5px 0 15px 0;
        display: flex;
        justify-content: center;

        button {
          width: 100px;
        }
      }
    }

    .expandsion-field {
      i {
        color: #66b1ff;
        position: relative;
        top: 2px;
      }

      text-align: right;
    }

    .mult-upload-container {
      padding: 40px;

      .top-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        span {
          color: gray;
          vertical-align: text-bottom;
        }
      }
    }
  }
}
</style>