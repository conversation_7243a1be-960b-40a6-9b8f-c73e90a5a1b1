<template>
  <div class="his-into-group">
    <div class="top-content">
      <div class="left-doctor">
        <div class="left-header">入组信息</div>
        <hos-form
          :model="dataForm_2"
          label-width="120px"
          size="medium"
          label-position="right"
          style="margin-right: 100px"
        >
          <hos-form-item label="科研用户">
            <span>{{ doctorName }}</span>
          </hos-form-item>
          <hos-form-item prop="subjectId" label="入组项目">
            <hos-select
              v-model="dataForm_2.subjectId"
              placeholder="请选择入组项目"
              style="width: 100%"
              @change="projectChange"
            >
              <hos-option
                v-for="i in projectList"
                :key="i.subjectId"
                :label="i.totalProjectName"
                :value="i.subjectId"
              />
            </hos-select>
          </hos-form-item>
        </hos-form>
        <div class="left-header">项目信息</div>
        <div>
          <div v-if="!dataForm_2.subjectId" style="padding:10px 0 20px;" class="empty">请选择入组项目后查看项目信息</div>
          <hos-form
            v-else
            :model="subjectInfo"
            v-loading="subjectLoading"
            label-width="120px"
            size="medium"
            label-position="right"
            style="margin-right: 100px"
          >
            <hos-form-item label="项目负责人">
              <span>{{ subjectInfo.projectAdminName }}</span>
            </hos-form-item>
            <template v-if="subjectInfo.otherOrgList && subjectInfo.otherOrgList.length>0">
              <hos-form-item v-if="subjectInfo.mainOrg && subjectInfo.mainOrg.orgName" label="主中心">
                <span>{{ subjectInfo.mainOrg.orgName }}</span>
              </hos-form-item>
              <hos-form-item label="分中心">
                <span>{{ subjectInfo.otherOrgList.map(it=>it.orgName).join('、') }}</span>
              </hos-form-item>
            </template>
            <template v-else-if="subjectInfo.mainOrg && subjectInfo.mainOrg.orgName">
              <hos-form-item label="项目所属中心">
                <span>{{ subjectInfo.mainOrg.orgName }}</span>
              </hos-form-item>
            </template>
            <template v-if="(subjectInfo.mainDept && subjectInfo.mainDept.deptName)">
              <hos-form-item label="主要参与科室">
                <span>{{ subjectInfo.mainDept.deptName }}</span>
              </hos-form-item>
              <hos-form-item v-if="subjectInfo.otherDeptList && subjectInfo.otherDeptList.length>0" label="其他参与科室">
                <span>{{ subjectInfo.otherDeptList.map((it) => it.deptName).join('、') }}</span>
              </hos-form-item>
            </template>
            <hos-form-item label="项目起止时间">
              <span v-if="subjectInfo.projectStartTime && subjectInfo.projectEndTime">
                <span>{{ subjectInfo.projectStartTime || '-' }}</span>
                <span style="margin:0 10px;">至</span>
                <span>{{ subjectInfo.projectEndTime || '-' }}</span>
              </span>
              <span v-else>暂无</span>
            </hos-form-item>
            <hos-form-item label="项目描述">
              <span>{{ subjectInfo.description || '暂无' }}</span>
            </hos-form-item>
          </hos-form>
        </div>
      </div>
      <div class="right-patient">
        <div class="right-header">患者信息</div>
        <div class="right-content">
          <div v-if="!dataForm_2.subjectId" class="empty">请选择入组项目后查看患者信息</div>
          <div v-else class="info">
            <case-info
              ref="caseIntoGroup"
              :doctor-id="userId"
              :patient-id="query.regNo"
              :project-id="projectId"
              :subject-id="dataForm_2.subjectId"
              :org-id="orgId"
              :form-readonly="formReadonly"
              :foreign-id="query.foreignId"
              @afterHisInGroup="afterHisInGroup"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <hos-button
        v-if="!formReadonly"
        :disabled="!dataForm_2.subjectId"
        type="primary"
        size="large"
        @click="dataFormSubmitHandle"
        >入组</hos-button
      >
      <!-- 没有选择项目不允许跳转 -->
      <hos-button :disabled="!dataForm_2.subjectId" type="text" size="large" @click="toEDC_patientListPage"
        >进入科研系统</hos-button
      >
    </div>
  </div>
</template>

<script>
import caseInfo from './case-info.vue'
import {aesEncrypt} from '@/utils/aes.js'
export default {
  components: {
    caseInfo
  },
  data() {
    return {
      subjectLoading:false,
      doctorName: '',
      dataForm_2: {
        subjectId: ''
      },
      projectId: '',
      // 所选项目是否启用审核
      subProjectEnableAudit: false,
      orgId: '',
      roleId: '',
      dataForm: {
        id: null,
        isHisCase: 0,
        regno: '',
        name: '',
        initials: '',
        idCard: '',
        gender: '',
        birthday: '',
        filterNo: '',
        sourceDeptId: '',
        intoDate: new Date(),
        empi: '',
        isNeedSyn: 1 // 数据是否同步成功,1代表需要同步(即是没有查询到外部接口数据),0代表不需要同步
      },
      centerName: '',
      userId: '',
      userName: '',
      projectList: [],
      formReadonly: false,
      // 患者在内部系统的信息,跳转到患者浏览页面会用到部分参数
      patientInfo: {
        
      },
      subjectInfo:{},
      baseEdcUrl: process.env.NODE_ENV === 'production' ? location.origin : 'http://localhost:9086'
    }
  },
  computed: {
    // 从url中获取的有用到的参数
    query() {
      return {
        // orgCode: this.$route.query.orgCode || 'HOS-HX',
        // foreignId: this.$route.query.foreignId || '17851',
        // regNo: this.$route.query.regNo || '**********'
        orgCode: this.$route.query.orgCode,
        foreignId: this.$route.query.foreignId,
        regNo: this.$route.query.regNo
      }
    }
  },
  created() {
    this.initApi().then(()=>{
      this.getHasIntoPsmProjectList()
    })
  },
  methods: {
    initApi(){
      return this.$axios
        .get(`/openApi/openapibase/apisystem/getAcessToken`, {
          params: {
            appId:'Wm7j11Em2fa4',
            appSecret:'65q1P96207G4k9H8',
          }
        })
        .then(({ data }) => {
          localStorage.setItem('his-api-access-token',data)
        })
    },
    getSubjectInfo(){
      if(!this.dataForm_2.subjectId || !this.userId){
        return
      }
      this.subjectLoading = true
      this.$axios
        .get(`/openApi/project/subject-cache-info/${this.dataForm_2.subjectId}/${this.userId}`)
        .then(({ data: res }) => {
          this.subjectInfo = res
        }).finally(()=>{
          this.subjectLoading = false
        })
    },
    // 获取用户拥有入组权限的项目列表
    getHasIntoPsmProjectList() {
      if (!this.query.foreignId) return
      this.$axios
        .get(`/openApi/project/${this.query.foreignId}/${this.query.orgCode}`, {
          params: {}
        })
        .then(({ data: res }) => {
          // 获取到科研用户姓名
          if (res && res.accountName) {
            this.doctorName = res.accountName
            this.userId = res.accountId
            this.orgId = res.orgId
          }
          if (res && res.hisPreProjModelList && res.hisPreProjModelList.length > 0) {
            // 拼数据
            const temArr = []
            res.hisPreProjModelList.forEach((item) => {
              // 单病种项目
              if (item.type === 1) {
                temArr.push({
                  ...item.subjectList[0],
                  subjectId: item.subjectList[0].id,
                  totalProjectName: item.subjectList[0].name
                })
              } else {
                const projectName = item.projectName
                item.subjectList.forEach((item2) => {
                  temArr.push({
                    ...item2,
                    subjectId: item2.id,
                    totalProjectName: projectName + '-' + item2.name
                  })
                })
              }
            })
            this.projectList = temArr
            if (temArr.length > 0) {
              // 不再默认选中第一个项目
              // this.projectChange(temArr[0].subjectId)
              // this.dataForm_2.subjectId = temArr[0].subjectId
            }
            console.log(this.projectList, '406')
          } else {
            this.projectList = []
          }
        })
    },
    // 选择的项目变化时,查询患者是否已经入组(患者empi在url中携带)
    projectChange(val) {
      if (!val) return
      // 保存项目id和中心id
      this.projectId = this.projectList.find((item) => item.subjectId === val).projectId
      this.roleId = this.projectList.find((item) => item.subjectId === val).roleId

      const projectInfo = this.projectList.find((item) => item.subjectId === val)
      console.log(projectInfo, '188')
      this.subProjectEnableAudit = projectInfo.isEnableAudit
      this.getSubjectInfo()

      this.$axios
        .get(`/openApi/patient/is/input`, {
          params: {
            subjectId: val,
            regno: this.query.regNo
          }
        })
        .then(({ data: res }) => {
          // 如果返回1 表示已经his入组过, 禁用表单
          // 如果返回0 表示没有his入组过, 可填写表单
          if (res) {
            this.formReadonly = true
            this.getInnerDataInfo(val).then(dataInfo=>{
              if(this.$refs.caseIntoGroup){
                this.$refs.caseIntoGroup.init(dataInfo)
              }
            })
          } else {
            this.formReadonly = false
            this.patientInfo = {}
            this.$refs.caseIntoGroup.init()
          }
        })

    },
    // 表单提交,这里有双重表单验证
    dataFormSubmitHandle() {
      this.$refs['caseIntoGroup'].dataFormSubmitHandle()
    },
    getTimestamp() {
      return this.$axios
        .get(`/openApi/custom-valid/time-stamp`).then(({ data: res }) => {
          return res
        })
    },
    // 进入edc系统，患者列表页面
    async toEDC_patientListPage() {
      const accountId = this.userId
      const timestamp = await this.getTimestamp()
      const aesString = aesEncrypt('signature_csm^'+timestamp)
      let redirect = encodeURIComponent(`/edc/subject-transfer?url=/edc/p/patient/patient-list&subjectId=${this.dataForm_2.subjectId}`)
      let href = `${this.baseEdcUrl}/csm/login-middle-v1/index?forHis=1&accountId=${accountId}&aesString=${aesString}&redirect=${redirect}`
      window.open(href,'csm_window')
    },
    afterHisInGroup() {
      this.formReadonly = true

      this.getInnerDataInfo(this.dataForm_2.subjectId)
    },
    // 获取患者在edc系统内部信息,外部接口数据用来展示表单,内部信息用来跳转
    getInnerDataInfo(id) {
      return this.$axios
        .get(`/openApi/patient/info`, {
          params: {
            subjectId: id,
            regno: this.query.regNo
          }
        })
        .then(({ data: res }) => {
          this.patientInfo = res
          console.log(this.patientInfo, '单独查询的接口-patientInfo')
          return res
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.his-into-group {
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .empty {
    color: #dcdfe6;
    text-align: center;
  }
  .top-content {
    flex-grow: 1;
    border: 1px solid #dcdfe6;
    border-bottom: none;
    display: flex;
    .left-doctor {
      flex: 1;
      // margin-right: 5px;
      // border-right: 1px solid #dcdfe6;
      .left-header {
        border-bottom: 1px solid #dcdfe6;
        padding: 15px 20px;
        margin-bottom: 20px;
      }
    }
    .right-patient {
      flex: 1;
      border-left: 1px solid #dcdfe6;
      display: flex;
      flex-direction: column;
      .right-header {
        border-bottom: 1px solid #dcdfe6;
        padding: 15px 20px;
      }
      .right-content {
        flex-grow: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        .info {
          width: 100%;
          height: 100%;
          padding-top: 20px;
          padding-right: 40px;
        }
      }
    }
  }
  .footer {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dcdfe6;
  }
}
</style>
