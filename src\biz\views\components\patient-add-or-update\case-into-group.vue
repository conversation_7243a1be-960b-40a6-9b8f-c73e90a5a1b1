<!--
  * @FileDescription：患者入组
-->
<template>
  <div class="case-into-group-container">
    <div v-loading="loading" class="case-content" :element-loading-text="loadingText">
      <div v-show="type === 1" class="single-form-container">
        <hos-form ref="dataForm" :model="dataForm" label-width="120px" size="large" label-position="right"
          style="margin-right: 30px">
          <hos-form-item :label="$t('所属中心')">
            <span>{{ centerName }}</span>
          </hos-form-item>
          <template v-for="(item, index) in baseData">
            <hos-form-item v-if="item.code == 'regno'" :key="index" :prop="item.code" :label="item.label"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-input v-model="dataForm[item.code]" :disabled="Boolean(dataForm.id)" :placeholder="item.label"
                @blur="getHisInfo(dataForm[item.code], 1)" @keyup.enter.native="getHisInfo(dataForm[item.code], 1)" />
            </hos-form-item>
            <!-- 病案号 -->
            <hos-form-item v-else-if="item.code == 'recordId'" :key="index" :prop="item.code" :label="item.label"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <!-- 病案号字段，单独配置了允许从外部接口获取数据才能调用接口 -->
              <hos-input v-if="item.isFillExdata" v-model="dataForm[item.code]" :disabled="Boolean(dataForm.id)" :placeholder="item.label"
                @blur="getHisInfo(dataForm[item.code])" @keyup.enter.native="getHisInfo(dataForm[item.code])" />
              
              <hos-input v-else v-model="dataForm[item.code]" :disabled="Boolean(dataForm.id)" :placeholder="item.label"/>
            </hos-form-item>
            <!-- 姓名 -->
            <hos-form-item v-else-if="item.code == 'name'" :key="index" :prop="item.code" :label="item.label"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-input v-model="dataForm[item.code]" auto-complete="off" maxlength="20" :placeholder="item.label"
                show-word-limit />
            </hos-form-item>
            <!-- 姓名缩写 -->
            <hos-form-item v-else-if="item.code == 'initials'" :key="index" prop="initials" :label="item.label"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-input v-model="dataForm['initials']" :placeholder="item.label" maxlength="20" show-word-limit
                disabled />
            </hos-form-item>
            <!-- 对身份验证 -->
            <hos-form-item v-else-if="item.code == 'idCard'" :key="index" :prop="item.code" :label="item.label"
              class="role-list" :rules="[
      { required: item.required, message: item.label + $t('为必填项'), trigger: 'blur' },
      {
        validator: 'regexp',
        pattern:
          /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: $t('请输入正确的身份证号码'),
        trigger: 'blur'
      }
    ]">
              <hos-input v-model="dataForm[item.code]" :placeholder="item.label" @blur="idCardBlur()" />
            </hos-form-item>
            <hos-form-item v-else-if="item.code == 'gender'" :key="index" :prop="item.code" :label="item.label"
              class="role-list"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-radio-group style="line-height:35px;" v-if="item.type=='radio'" v-model="dataForm[item.code]">
                <hos-radio v-for="i in genderList" :key="i.id" :label="+i.value">
                  {{ i.label }}</hos-radio>
              </hos-radio-group>
              <hos-select v-else v-model="dataForm[item.code]" :placeholder="item.label" style="width: 100%">
                <hos-option v-for="i in genderList" :key="i.id" :label="i.label" :value="+i.value" />
              </hos-select>
            </hos-form-item>
            <hos-form-item v-else-if="item.code == 'birthday'" :key="index" :prop="item.code" :label="item.label"
              class="role-list"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-date-picker v-model="dataForm[item.code]" type="date" value-format="yyyy-MM-dd"
                :placeholder="item.label" style="width: 100%" :picker-options="option" />
            </hos-form-item>
            <hos-form-item v-else-if="item.code == 'intoDate'" :key="index" :prop="item.code" :label="item.label"
              class="role-list"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-date-picker v-model="dataForm[item.code]" :disabled="Boolean(dataForm.id)" type="date"
                value-format="yyyy-MM-dd" :placeholder="item.label" style="width: 100%" :picker-options="option" />
            </hos-form-item>
            <!-- 中心唯一字段/系统唯一字段/项目唯一字段 -->
            <hos-form-item
              v-else-if="item.code == 'filterNo' || item.code == 'sysUniqueId' || item.code == 'projUniqueId'"
              :key="index" :prop="item.code" :label="item.label" class="role-list"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-input v-model="dataForm[item.code]" :placeholder="item.label" />
            </hos-form-item>
            <!-- 手机号 -->
            <hos-form-item v-else-if="item.code == 'phone'" :key="index" :prop="item.code" :label="item.label" :rules="[
      { required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] },
      { validator: 'regexp', pattern: /^1[0-9]{10}$/, message: $t('手机号码格式不正确'), trigger: 'blur' }
    ]">
              <hos-input v-model="dataForm[item.code]" auto-complete="off" :placeholder="item.label" show-word-limit />
            </hos-form-item>
            <hos-form-item v-else-if="item.code == 'groupIds'" :key="index" :prop="item.code" :label="item.label"
              class="role-list"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-select v-model="dataForm[item.code]" clearable :placeholder="item.label" style="width: 100%">
                <hos-option v-for="i in groupList" :key="i.id" :label="i.name" :value="i.id" />
              </hos-select>
            </hos-form-item>
            <hos-form-item v-else-if="item.code == 'labelIds'" :key="index" :prop="item.code" :label="item.label"
              class="role-list"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-select v-model="dataForm[item.code]" multiple :placeholder="item.label" style="width: 100%">
                <hos-option v-for="i in tagList" :key="i.id" :label="i.name" :value="i.id" />
              </hos-select>
            </hos-form-item>
            <hos-form-item v-else-if="item.code == 'diseaseId'" :key="index" :prop="item.code" :label="item.label"
              class="role-list"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-select v-model="dataForm[item.code]" clearable :placeholder="item.label" style="width: 100%">
                <hos-option v-for="i in diseaseList" :key="i.id" :label="i.name" :value="i.id" />
              </hos-select>
            </hos-form-item>
            <!-- 基础字段code不能匹配时 -->
            <hos-form-item v-else :key="index" :prop="item.code" :label="item.label"
              :rules="[{ required: item.required, message: item.label + $t('为必填项'), trigger: ['blur', 'change'] }]">
              <hos-input v-model="dataForm[item.code]" :placeholder="item.label" />
            </hos-form-item>
          </template>
        </hos-form>
        <hos-row>
          <case-expand-form ref="ExpandProps" :meta-data="metaData" :gender-list="genderList" :getExternalFieldList="getExternalFieldList"
            :external-result="externalResult" :is-center-case="isCenterCase" :patient-id="patientId" @getHisInfo="getHisInfo" />
        </hos-row>
        <hos-row class="button-row">
          <hos-button type="primary" @click="dataFormSubmitHandle">{{ $t('保存') }} </hos-button>
          <hos-button v-if="showBackBtn" @click="$emit('backToList')"> {{ $t('返回') }}</hos-button>
        </hos-row>
      </div>
      <div v-show="type === 2">
        <!-- 全程项目批量导入 -->
        <template v-if="subjectInfo.projectType == 3">
          <div class="qc-disease-select">
            <span class="label">{{ $t('请选择批量入组的目标病种') }}</span>
            <hos-select v-model="currentDiseaseId" style="width: 300px" :data="diseaseMainList" label-key="name" value-key="id"> </hos-select>
          </div>

          <!-- 选择主库还是要走原来的逻辑 -->
          <patient-batch-upload v-if="currentDiseaseId == '0'" ref="patientUpload" :custom-style="customStyle" :before-download="downloadTemplate" :before-upload="beforeUpload"
          :after-upload="afterUpload" :download-template-option="downloadTemplateOption" :upload-file-option="uploadFileOption" />
          
          <!-- TODO:选择病种走新配置逻辑 -->
          <patient-batch-upload v-else ref="patientUpload" :custom-style="customStyle" :before-download="downloadTemplate" :before-upload="beforeUpload"
          :after-upload="afterUpload" :download-template-option="QcdownloadTemplateOption" :upload-file-option="QcUploadFileOption" />
        </template>
        
        <!-- 通用项目批量导入 -->
        <template v-else>
          <patient-batch-upload ref="patientUpload" :custom-style="customStyle" :before-download="downloadTemplate" :before-upload="beforeUpload"
          :after-upload="afterUpload" :download-template-option="downloadTemplateOption" :upload-file-option="uploadFileOption" />
        </template>
      </div>
    </div>
    <!-- 选择字段弹出框 -->
    <search-item-select ref="searchItemDialog" :is-patient-batch-import="true" :single-select="false" :tabShow="tabShow"
      :patient-select="true" :diseaseId="currentDiseaseId" @close-search-dialog="fillResult" />
  </div>
</template>

<script>
import SearchItemSelect from '@/components/querybuilder-edc/SearchItemSelect'
import CaseExpandForm from './case-expand-form'
import { debounce } from 'lodash'
import { basicInfoFieldList, basicInfoFieldObj } from '@/utils/enum'
import { pinyin } from '@/utils/Convert_Pinyin'
import { formateDate } from '@/utils/date'
import PatientBatchUpload from '@/components/upload/index.vue' 
export default {
  components: {
    SearchItemSelect,
    CaseExpandForm,
    PatientBatchUpload
  },
  props: {
    patientId: {
      type: String,
      default: ''
    },
    showBackBtn: {
      type: Boolean,
      default: true
    },
    // hasBackPath: {
    //   type: Boolean,
    //   default: false
    // },
    type: {
      type: Number,
      default: 1
    },
    isForHistory:{
      type:Boolean,
      default(){
        return false
      }
    }
  },
  data() {
    return {
      currentDiseaseId: '0',
      orgList: [],
      tabPosition: 'left',
      dataForm: {
        id: null,
        isHisCase: 0,
        regno: '',
        name: '',
        initials: '',
        idCard: '',
        gender: '',
        birthday: '',
        filterNo: '',
        sourceDeptId: '',
        intoDate: formateDate()
      },
      loading: false,
      loadingText: this.$t('正在获取表单信息，请等待...'),
      genderList: [],
      isHisCaseEnum: [],
      // isHisCaseEnum: [{
      //   value: 1,
      //   label: '本院患者'
      // }, {
      //   value: 0,
      //   label: '外院患者'
      // }],
      option: {
        disabledDate: (time) => {
          return time.getTime() > Date.now() - 1 * 24
        }
      },
      showExpandTips: false,
      showExpandForm: false,
      originalMetaData: [], // 包括基本字段在内的数组
      baseData: [],
      metaData: [], // 过滤基本字段后的数组，避免基本字段在拓展表单中重复展示
      externalResult: [],
      customStyle: "width: 50%;margin:auto;padding-top:100px;",
      QcdownloadTemplateOption: {
        method: 'POST', // 请求方式
        apiUrl: 'edc/patient/disease/download-excel-template', // 下载模板Api对应的接口地址
        params: {
          subjectId: '',
          diseaseId: ''
        }, // 拼到请求路径上的参数
        data: {}, // body参数 POST请求时用到
        templateFileName: this.$t('患者批量导入模板')
      },
      downloadTemplateOption: {
        method: 'POST', // 请求方式
        apiUrl: 'edc/patient/download-excel-template', // 下载模板Api对应的接口地址
        params: {
          subjectId: ''
        }, // 拼到请求路径上的参数
        data: {}, // body参数 POST请求时用到
        templateFileName: this.$t('患者批量导入模板')
      },
      QcUploadFileOption: {
        uploadTypeState: false, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
        method: 'POST',
        apiUrl: 'edc/patient/disease/excel-into-patient',
        params: {},
        data: {
          accountId: '',
          subjectId: '',
          diseaseId: ''
        },
      },
      uploadFileOption: {
        uploadTypeState: false, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
        method: 'POST',
        apiUrl: !this.isForHistory ? 'edc/patient/excel-into-patient' : 'edc/patient/excel-into-patient-by-model',
        params: {},
        data: {
          accountId: '',
          subjectId: ''
        },
      },
      // 是否允许从外部接口获取数据 0表示不允许
      isCenterCase: 0,
      groupList:[],
      diseaseList: [],
      tagList:[]
    }
  },
  computed: {
    diseaseMainList() {
      const temArr = [{name: this.$t('主库'), id: '0'}]
      return temArr.concat(this.diseaseList)
    },
    expandTips() {
      return this.showExpandForm ? this.$t('收起') : this.$t('点击展开患者拓展信息')
    },
    orgId() {
      if (this.subjectInfo.loginUserOrgId) {
        return this.subjectInfo.loginUserOrgId
      } else if (this.orgList.length > 0) {
        const tmp = this.orgList.filter((item) => item.isPrimary)
        if (tmp.length > 0) {
          // 返回主中心
          return tmp[0].orgId
        } else {
          // 返回第一个中心
          return this.orgList[0].orgId
        }
      } else {
        return null
      }
    },
    centerName() {
      const tmp = this.orgList.filter((item) => item.orgId == this.orgId)
      if (tmp.length > 0) {
        return tmp[0].orgName
      } else {
        return ''
      }
    },
    tabShow(){
      if(this.isForHistory){
        return ['patient','crf']
      }else{
        return undefined
      }
    }
  },
  watch: {
    'dataForm.name'(newV, oldV) {
      this.getInitial(newV)
    }
  },
  async created() {
    this.getDictItem()
    this.getOrgList()
    if(this.subjectInfo.configJson.isEnablePatientTag){
      await this.getTagList()
    }
    if(this.subjectInfo.configJson.isEnablePatientGroup){
      await this.getGroupList()
    }

    if (this.subjectInfo.projectType && this.subjectInfo.projectType == 3) {
      this.getDiseaseList()
    }

    // 获取项目拓展字段，并渲染成表单控件，为了保证从病历列表点击修改条状回显，这里必须await先获取到拓展字段，再getinfo,否则修改患者拓展信息无法回显
    await this.getCaseExpandField()

    if (this.patientId) {
      this.dataForm.id = this.patientId
      this.getInfo()
      // 如果是点击修改跳转过来的，默认展开拓展字段表单
      this.showExpandForm = true
    } else {
      return
    }
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.roleIdListDefault = []
        if (!this.dataForm.id) {
          return
        }
        this.getDictItem().then(() => {
          if (this.dataForm.id) {
            this.getInfo()
          }
        })
      })
    },
    async getGroupList(){
      const {data} = await this.$api('biz.edc.patient.patientList.getGroupList', { subjectId: this.subjectId })
      this.groupList = data || []
    },
    async getTagList(){
      const {data} = await this.$api('biz.edc.patient.patientList.getTagList', { subjectId: this.subjectId })
      this.tagList = data || []
    },
    async getOrgList() {
      if (this.subjectInfo.allOrgList) {
        // 从缓存获取所有子项目机构
        this.orgList = this.deepClone(this.subjectInfo.allOrgList)
      } else {
        const {data} = await this.$api('biz.edc.project.projectOrg.queryAllProjectOrg', { projectId: this.projectId })
        this.orgList = data || []
      }
    },
    async getExternalFieldList(categoryId){
      const res = await this.$api('biz.edc.interface.query-attribute.getAllByCategoryId', categoryId)
      return res.data || []
    },
    async getDiseaseList() {
      const { code, data } = await this.$api('biz.edc.disease.queryAllApi', {
        subjectId: this.subjectId
      })
      if (code == 200) {
        this.diseaseList = data
      } else {
        this.diseaseList = []
      }
    },
    // 获取信息
    getInfo() {
      this.$api('biz.edc.patient.patientList.queryDetailApi', {id:this.dataForm.id,isDesensitization:0})
        .then(({ data: res }) => {
          if(res.labelIds){
            res.labelIds = res.labelIds.split(',')
          }
          res.extJson = res.extJson ? JSON.parse(res.extJson) : {}
          // 将ext类字段视作自定义字段，编辑时在extJson中编辑，保存时保存到外层form对象中
          this.$extFields.forEach(key=>{
            if(res[key] !== undefined){
              res.extJson[key] = res[key]
              delete res[key]
            }
          })
          this.dataForm = {
            ...this.dataForm,
            ...res
          }
          // this.metaData = {
          //   ...this.dataForm,
          //   ...res
          // }
          // 拓展字段表单的回显
          // this.$refs['ExpandProps'].formModel = res
          this.$refs['ExpandProps'].formModel = res.extJson
          // 保存拓展字段的初始值,随访参考点要更根据初始值的有无判断是否可以修改
          this.$refs['ExpandProps'].initFormValue = this.deepClone(res.extJson || {})
        })
        .catch(() => { })
    },
    // 根据登记号获取信息
    getHisInfo(pin, type) {
      // 如果中心配置-数据来源 配置不允许通过外部接口数据获取，则不调用接口
      if (!this.isCenterCase) return
      // 当患者不是his患者时，直接return
      if (!pin) {
        return
      }
      const params = {
        subjectId: this.subjectId,
        orgId: this.orgId
      }
      if (type) {
        params.regno = pin
      } else {
        params.pin = pin
      }
      // 当患者是his患者时，调用his接口，获取患者信息，回显
      this.$api(
        type ? 'biz.edc.patient.getHisBasicInfoByRegno' : 'biz.edc.patient.getHisBasicInfoByPin', params
      ).then(({data:res}) => {
          // 如果填写了错误或者不存在的登记号导致没有拉取到his信息，不回显，当作新患者填写
          if (res && (res.regno || res.recordId)) {
            this.dataForm = {
              ...this.dataForm,
              ...res,
              isHisCase: 1,
              intoDate: res.intoDate ? res.intoDate : formateDate()
            }
          }
        })
        .catch(() => { })

      // 如果本项目包含拓展字段并且拓展字段中有字段配置了外部接口，调接口获取该字段的值
      const temArr = []
      this.metaData.map((item) => {
        if (item.externaldataInfo && item.externaldataInfo.CatID) {
          item.ID = item.code
          item.externaldataInfo.ID = item.code
          temArr.push(item.externaldataInfo)
        }
      })
      if (temArr.length > 0) {
        this.$api('biz.edc.interface.data.getExternalDataIntoGroup',{
            itemStr: JSON.stringify(temArr),
            regno: pin,
            admIds: '=' // 关联的就诊id??有这个概念吗??
          }).then(({data:res}) => {
            console.log(res, 'externaldataInfo')
            // FIXME:接口掉通过后将外部接口字段信息显示到表单中
            if (res && typeof res === 'string') {
              this.externalResult = JSON.parse(res)
            } else {
              this.externalResult = res
            }
            // this.externalResult = [
            //   {
            //       "DataObject": [
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           },
            //           {
            //               "result": "秦海贤"
            //           }
            //       ],
            //       "Message": "tuo",
            //       "Result": true
            //   }
            // ]

            // 让子组件回显设置了外部接口的字段
            this.$nextTick(() => {
              this.$refs.ExpandProps.setExternalResult()
            })
          })
      }
    },
    getInitial(name) {
      this.dataForm.initials = pinyin.getCamelChars(name)
    },
    // 表单提交,这里有双重表单验证
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          // 患者拓展信息验证
          this.$refs['ExpandProps'].$refs['form'].validate((valid2) => {
            if (!valid2) {
              this.$message({
                message: this.$t('请填写患者拓展信息'),
                type: 'warning',
                duration: 1000
              })
              if (!this.showExpandForm) {
                this.showExpandFormHandle()
              }
              return false
            } else {
              // 获取拓展信息表单的填写内容
              const expandFormValue = this.deepClone(this.$refs['ExpandProps'].formModel)
              const dataForm = this.deepClone(this.dataForm)
              this.$extFields.forEach(key=>{
                if(expandFormValue[key] !== undefined){
                  dataForm[key] = expandFormValue[key]
                  delete expandFormValue[key]
                }
              })
              this.loading = true
              this.loadingText = this.$t('正在保存，请等待...')
              // 记录是新增还是修改
              const isNew = !this.dataForm.id
              this.$api(
                !this.dataForm.id ? 'biz.edc.patient.patientList.addApi' : 'biz.edc.patient.patientList.editApi',
                {
                  ...dataForm,
                  labelIds: Array.isArray(this.dataForm.labelIds) ? this.dataForm.labelIds.join(',') : '',
                  intoUserId: !this.dataForm.id ? this.userId : this.dataForm.intoUserId,
                  projectId: this.projectId,
                  subjectId: this.subjectId,
                  orgId: this.orgId, //
                  extJson: JSON.stringify(this.getExpandField(expandFormValue))
                }
              )
                .then((res) => {
                  // 新增成功后重置表单
                  if (isNew) {
                    this.$emit('afterJoin')
                  } else {
                    // 修改后返回患者列表
                    this.$emit('afterUpdate')
                  }

                  this.$refs['dataForm'].resetFields()
                  // 重置拓展表单
                  this.$refs['ExpandProps'].$refs['form'].resetFields()
                  this.$nextTick(() => {
                    if (this.$refs['dataForm']) {
                      this.$refs['dataForm'].clearValidate()
                    }
                    if (this.$refs['ExpandProps'] && this.$refs['ExpandProps'].$refs['form']) {
                      this.$refs['ExpandProps'].$refs['form'].clearValidate()
                    }
                  })
                  this.$message({
                    message: this.$t('操作成功'),
                    type: 'success',
                    duration: 500,
                    onClose: () => {
                      this.loading = false
                    }
                  })
                })
                .catch(() => {
                  this.loading = false
                })
            }
          })
        })
      },
      1000,
      {
        leading: true,
        trailing: false
      }
    ),
    // 获取字典项
    getDictItem() {
      this.getDictItemByCode('gender', 'genderList')
      this.getDictItemByCode('isHisCaseEnum', 'isHisCaseEnum')
    },
    // 输入身份证号失焦后根据校验校验结果与日期选择器联动
    idCardBlur() {
      const pattern = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
      // 校验未通过时，return
      if (!pattern.test(this.dataForm.idCard)) {
        return
      }
      // 校验通过，联动日期选择器
      let birthStr = this.dataForm.idCard.slice(6, 14)
      birthStr = `${birthStr.slice(0, 4)}-${birthStr.slice(4, 6)}-${birthStr.slice(6)}`
      this.dataForm.birthday = birthStr
    },
    getExtension(nameStr) {
      const arr = nameStr.split('.')
      const len = arr.length
      return arr[len - 1]
    },
    getExpandField(obj) {
      basicInfoFieldList.forEach((item) => {
        delete obj[item]
      })
      return obj
    },
    // 上传文件之前的钩子
    handlePreUpload(file) {
      const extension = this.getExtension(file.name).toLocaleLowerCase()
      if (!'.xls,.xlsx'.includes(extension)) {
        this.$message.error(this.$t('文件格式不符合要求'))
        return false
      }
      return true
    },
    // 打开表单字段弹出框
    openResultDialog() {
      this.$refs.searchItemDialog.crfCateItems = []
      this.$refs.searchItemDialog.openSearchDialog(true)
    },
    downloadTemplate() {
      this.openResultDialog()
    },
    beforeUpload() {
      // 通用项目
      this.uploadFileOption.data.accountId = this.userId
      this.uploadFileOption.data.subjectId = this.subjectId

      // 全程项目
      this.QcUploadFileOption.data.accountId = this.userId
      this.QcUploadFileOption.data.subjectId = this.subjectId
      this.QcUploadFileOption.data.diseaseId = this.currentDiseaseId
    },
    afterUpload() {
      // 以前是导入成功后跳转到导入记录...
      // this.$router.push({
      //   path: '/patient/import-record',
      //   query: {
      //     id: this.$route.query.id,
      //     subid: this.$route.query.subid
      //   }
      // })
    },
    // 获取导出模板的字段，并导出
    fillResult(activeItems) {
      if (!activeItems) {
        return
      }
      // 通用项目
      this.downloadTemplateOption.params.subjectId = this.subjectId
      this.downloadTemplateOption.data = activeItems
      
      // 全程
      this.QcdownloadTemplateOption.params.subjectId = this.subjectId
      this.QcdownloadTemplateOption.params.diseaseId = this.currentDiseaseId
      this.QcdownloadTemplateOption.data = activeItems
      this.$refs.patientUpload.downloadTemplate()
    },
    isExtField(code){
        return this.$extFields.indexOf(code) > -1
    },
    // 获取项目所有的拓展患者拓展字段
    async getCaseExpandField() {
      this.loading = true
      this.loadingText = this.$t('正在获取表单信息，请等待...')
      let fields = []
      if (this.subjectInfo && this.subjectInfo.patientAttr) {
        // 从缓存中获取
        fields = this.deepClone(this.subjectInfo.patientAttr.fields)
        this.isCenterCase = this.subjectInfo.patientAttr.isCenterCase
      } else {
        const params = {
          projectId: this.projectId,
          subjectId: this.subjectId,
          orgId: this.orgId
        }
        const res = await this.$api('biz.edc.subProject.queryOrgPatientListAttr', params)
        fields = res.data.fields || []
        this.isCenterCase = this.subjectInfo.patientAttr.isCenterCase
      }
      this.loading = false
      fields = fields.filter((item) => item.isShow)
      // 拓展字段中有必填项才需要给出提示，并填写
      if (fields && fields.length > 0) {
        this.showExpandTips = true

        // const temArr = []
        // if (res.filter(i => i.code == 'name').length == 0) {
        //   temArr.concat(basicInfoFieldArr)
        // }
        const curBaseData = []
        const curMetaData = []
        fields.map(async (item, index) => {
          // if (item.code == 'name' || item.code == 'gender' || item.code == 'birthday' || item.code == 'filterNo' || item.code == 'intoDate' || item.isShow) {
          if (item.isShow === null || item.isShow) {
            if (item.isBaseField && !this.isExtField(item.code)) {
              curBaseData.push({
                id: item.id,
                // key: item.widgetType + '_' + index,
                label: item.name,
                code: item.code,
                required: item.isRequired === 1,
                type: item.widgetType ? item.widgetType : this.getType(item.code),
                isShow: 1,
                sort: item.listSort,
                widgetOption: item.widgetOption ? item.widgetOption : '',
                externaldataInfo: item.externalDataInfo ? JSON.parse(item.externalDataInfo) : '',
                isFillExdata: item.isFillExdata // 用来控制病案号是否可以用来获取外部数据
              })
            } else {
              let options = []
              if(item.optionType == 1){
                // 通过字典获取options
                if(item.dictCode){
                  const dictOptions = await this.getDictItemByCode(item.dictCode)
                  options = dictOptions
                }
              }else{
                options = item.widgetOption ? JSON.parse(item.widgetOption) : []
              }
              curMetaData.push({
                id: item.id,
                // key: item.widgetType + '_' + index,
                label: item.name,
                code: item.code,
                required: item.isRequired === 1,
                type: item.widgetType ? item.widgetType : this.getType(item.code),
                isShow: 1,
                sort: item.listSort,
                options,
                externaldataInfo: item.externalDataInfo ? JSON.parse(item.externalDataInfo) : '',
                isVisitReferenceDate: item.isVisitReferenceDate, // 是否可设置为随访参考点
                isUsedVisitPlan: item.isUsedVisitPlan, // 是否当前随访流程参考点
              })
            }
          }
        })
        // this.originalMetaData = temArr
        // 过滤是为了避免再到拓展字段表单组件中去修改加判断条件
        // 基础字段
        // const curBaseData = this.originalMetaData.filter(item => basicInfoFieldList.includes(item.code))
        this.baseData = curBaseData.sort(function (a, b) {
          if (a.sort < b.sort) {
            return -1
          } else if (a.sort === b.sort) {
            return 0
          } else {
            return 1
          }
        })
        // 扩展字段
        // const curMetaData = this.originalMetaData.filter(item => !basicInfoFieldList.includes(item.code))
        this.metaData = curMetaData.sort(function (a, b) {
          if (a.sort < b.sort) {
            return -1
          } else if (a.sort === b.sort) {
            return 0
          } else {
            return 1
          }
        })
        console.log(this.baseData, 'baseData')
        console.log(this.metaData, 'metaData')
      }
    },
    // 展示拓展字段表单
    showExpandFormHandle() {
      this.showExpandForm = !this.showExpandForm
    },
    getType(code) {
      return basicInfoFieldObj[code]
    }
  }
}
</script>