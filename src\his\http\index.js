import axios from 'axios'
import { Message } from 'hosui'

// 记录和显示错误
function errorLog( info ) {
  // 显示提示
  Message( {
    message: info,
    type: 'error',
    showClose: true,
    duration: 5 * 1000
  } )
}

// 创建一个 axios 实例
const service = axios.create( {
  baseURL: `/csm`,
  timeout: 20000,
  withCredentials: true
} )

/**
 * 请求拦截
 */
service.interceptors.request.use( config => {

  // 防止缓存，GET请求默认带_t参数
  if ( config.method === 'get' ) {
    config.params = {
      ...config.params,
      ...{
        '_t': new Date().getTime()
      }
    }
  }
  config.params = {
    ...config.params,
    // ...makeHisEncryptParams(router.currentRoute.query.foreignId)
  }
  // config.headers['Access-Token'] = 'S8pG6ZTMrPiM-qii-Mj71m0chq4lPzoIaAW1HOPX04q2q5n-6jlkp1uQYk_7645Yyc-O5aBwc-mbEWvSFHLdEkZgXDeJoIurAqNfFjY_AXnrPf6yTX4Ha_OyC9qs_FbC'
  config.headers['apiAccessToken'] = localStorage.getItem('his-api-access-token')
  // console.log('config',config)
  return config
}, error => {
  return Promise.reject( error )
} )

/**
 * 响应拦截
 */
service.interceptors.response.use( response => {
  // 具体的业务数据错误处理
  if ( !response.data || !response.data.success ) {
    let message = response.data ? response.data.msg : '请求失败'
    errorLog( message )
    return Promise.reject( response.data )
  } else {
    return response.data
  }
}, error => {
  if ( error.response && error.response.status === 500 ) {
    error.message = '系统服务器异常, 请联系管理员'
  }

  if ( error.message === 'Network Error' ) {
    error.message = '服务器请求失败，请检查网络连接'
  }
  errorLog( error.message )
  return Promise.reject( error )
} )

export default service