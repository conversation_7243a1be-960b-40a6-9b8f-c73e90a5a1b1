<template>
  <div id="app" :class="getClass()">
    <router-view ref="routerView"></router-view>
    <db-dialog v-if="!hisDbdialog"></db-dialog>
  </div>
</template>

<script>
// import { clearAll } from '@base/utils/base/storage-util'
// import AuthConstant from '@base/constant/auth-constant.js'
import globalMixin from '@/mixins/globalMixin'
import unloadMixin from '@base/mixins/unloadMixin'
import openMenu from '@base/utils/base/menu'
import dbDialog from '@base/components/DHCWebBrowser-dialog' //医为客户端弹窗
import { returnGlobalValue } from '@base/utils'
import { mapState } from 'vuex'

export default {
  name: 'App',
  mixins: [globalMixin, unloadMixin],
  // mixins: [globalMixin],
  components: {
    dbDialog
  },
  data() {
    return {
      // 如果为true,则隐藏dbDialog,由his进行弹窗提醒
      hisDbdialog: returnGlobalValue('VUE_APP_HIS_DBDIALOG'),
      unbindMessage: null,
      addinsWs: null
    }
  },
  computed: {
    ...mapState({
      dialogUid: (state) => state.sys.dialogUid
    })
  },
  created() {
    // fix-hos 每次进入系统都更新登录类型缓存
    this.getLoginType()
    // 初始化对外提供的全局方法
    this.initGlobalMethods()
    // 在其他页面通过postMessage,调用编辑菜单弹窗
    this.unbindMessage = this.$messageEvent.on(this.messageHandle)
    // 只保存biz下的vuex内容
    if (this.$ls.get('app')) {
      this.$store.state.app = this.$ls.get('app')
    }
    // 在页面刷新时将vuex-biz里的信息保存到sessionStorage里
    window.addEventListener('beforeunload', () => {
      this.$ls.set('app', this.$store.state.app)
    })
    // 在页面加载时读取sessionStorage里的状态信息
    // if (sessionStorage.getItem("store")) {
    //   this.$store.replaceState(
    //     Object.assign(
    //       {},
    //       this.$store.state,
    //       JSON.parse(sessionStorage.getItem("store"))
    //     )
    //   );
    // }
    //
    // // 在页面刷新时将vuex里的信息保存到sessionStorage里
    // window.addEventListener("beforeunload", () => {
    //   sessionStorage.setItem("store", JSON.stringify(this.$store.state));
    // });
  },
  mounted() {
    // initWebsys()
  },
  updated() {
    // App.vue更新时,判断routerView是否有内容
    // 有内容将loading隐藏,无内容将loading显示
    if (this.$refs.routerView) {
      const loading = document.querySelector('#login-loading')
      if (loading) loading.style.display = 'none'
    } else {
      const loading = document.querySelector('#login-loading')
      if (loading) loading.style.display = 'block'
    }
  },
  methods: {
    initGlobalMethods() {
      // 全局添加一个openMenu方法,传入传入menu对象或menu.name(code)
      window.openMenu = openMenu.bind(this)
      // 添加hos_前缀,防止被同名方法覆盖
      window.hos_openMenu = openMenu.bind(this)
      // 添加关闭弹窗方法,支持传参.
      window.hos_closeDialog = (uid) => {
        this.$store.commit('CLOSE_DIALOG', { _uid: uid || this.dialogUid })
      }
      // 添加全局获取iframeDOM的方法.支持传参
      window.hos_getCurrentIframe = this.getCurrentIframe.bind(this)
    },
    // 根据传参获取相应的iframeDOM,若不传参获取当前页签下的iframeDOM.
    getCurrentIframe(code) {
      const iframeList = document.querySelectorAll('iframe[data-name]')
      if (!iframeList) return null
      let currentIframe
      // 如果传入code,则根据code获取iframe,如果未传入,则返回当前页签下的iframe
      if (code) {
        code = code.startsWith('/') ? code.substring(1) : code
        iframeList.forEach((item) => {
          item.dataset.name === code && (currentIframe = item)
        })
      } else {
        iframeList.forEach((item) => {
          const style = window.getComputedStyle(item)
          style.display !== 'none' && (currentIframe = item)
        })
      }
      return currentIframe
    },
    messageHandle(e) {
      const { type, data } = e.data
      if (type === 'editMenu') {
        this.openEditMenu(data)
      }
    },
    openEditMenu(id) {
      this.$store.commit('OPEN_DIALOG', {
        component: require('@base/views/sys/resource/edit.vue').default,
        _uid: 'resourceEditDialogGloble',
        props: {
          id: id,
          status: 'edit:',
          class: 'dialog-form-tabs'
        }
      })
    },
    getClass() {
      let result = []
      if (navigator.userAgent.indexOf('Chrome/49') !== -1) {
        result.push('chrome49')
      }
      return result
    },
    beforeunloadHandler(e) {
      e = e || window.event
      // if (!(window.performance && window.performance.navigation.type === 1)) { // 如果不是刷新是关闭浏览器窗口
      e.preventDefault()
      e.returnValue = '自定义文本'
      return '自定义文本'
      // } else {
      //   return true
      // }
    },
    // fix-hos
    getLoginType() {
      this.$api('biz.index.getLoginModel').then(res => {
        localStorage.setItem('loginType', JSON.stringify(res.data))
      })
    }
  },
  beforeDestroy() {
    this.unbindMessage()
    window.removeEventListener('beforeunload', (e) => this.beforeunloadHandler(e))
  }
}
</script>

<style>
@import './sys/hos-app-base/assets/style/custom.css';
@import './sys/hos-app-base/assets/style/font-awesome-4.7.0/css/font-awesome.min.css';
html,
body {
  margin: 0 !important;
  padding: 0;
}
.github-corner:hover .octo-arm {
  animation: octocat-wave 560ms ease-in-out;
}
@keyframes octocat-wave {
  0%,
  100% {
    transform: rotate(0);
  }
  20%,
  60% {
    transform: rotate(-25deg);
  }
  40%,
  80% {
    transform: rotate(10deg);
  }
}
@media (max-width: 500px) {
  .github-corner:hover .octo-arm {
    animation: none;
  }
  .github-corner .octo-arm {
    animation: octocat-wave 560ms ease-in-out;
  }
}
</style>
