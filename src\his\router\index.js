import Vue from 'vue'
import Router from 'vue-router'
import hisIntoGroup from '../views/his-into-group'

Vue.use(Router)

// 处理重复跳转报错提示
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
}

const constantRouterMap = [
    // his-into-common: 常规的HIS入组， 后续有其他特殊处理的，单独新增页面
    {
        path: '/his-into-common',
        // path: '/his-into-group',
        name: 'his-into-group',
        component: hisIntoGroup,
        meta: { title: 'his患者入组' },
    },
]

const router = new Router({
    mode: 'hash', // history
    base: process.env.BASE_URL,
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRouterMap
})

// 使用全局导航守卫设置标题
router.beforeEach((to, from, next) => {
    // 根据路由元信息设置标题
    if (to.meta.title) {
        document.title = to.meta.title;
    }
    next();
});


export default router
