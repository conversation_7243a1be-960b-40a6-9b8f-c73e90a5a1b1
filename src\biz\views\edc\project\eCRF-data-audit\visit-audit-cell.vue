<!-- 访视进度单元 -->
<template>
    <div class="visit-progress-cell">
        <hos-popover v-for="visit in visits" :key="visit.id" placement="bottom" width="330" trigger="hover"
            :visible-arrow="false" popper-class="custom-tips-popover">
            <div class="popover-content">
                <div>{{ $t('随访名称') }}：{{ visit.name }}</div>
                <div>{{ $t('随访状态') }}：{{ statusMap[visit.visitWindowsStatus] ? statusMap[visit.visitWindowsStatus] : '-' }}</div>
                <div>{{ $t('计划随访日期') }}：{{ visit.planDate ? visit.planDate : '-' }}</div>
                <div>
                    {{ $t('随访窗口时间') }}：{{ visit.startDate ? visit.startDate : '' }}{{ visit.startDate ? $t('至') :
                    '-' }}{{ visit.endDate ? visit.endDate : '' }}
                </div>
                <div>{{ $t('实际随访日期') }}：{{ visit.realTime ? visit.realTime : '-' }}</div>
                <div>{{ $t('随访详情') }}：
                    <span v-for="(value, key, index) in visit.groupedFormMap" :key="index">
                        {{ formStatusMap[key].label + $t('：') + value.length }}
                    </span>
                </div>
            </div>
            <div slot="reference" class="visit-status-popover">
                <span class="visit-cell" :title="$t('点击查看详情')"
                    @click="clickVisit(visit)">
                    <span v-for="(value, key) in visit.groupedFormMap" :key="key" class="gorm-status-group">
                        <img :src="formStatusMap[key].path" class="status-img">
                        <span class="status-num">{{ value.length }}</span>
                    </span>
                </span>
            </div>
        </hos-popover>
    </div>
</template>

<script>
import { visitStatusMap } from '@/utils/enum.js'
export default {
    name: 'VisitInputCell',
    props: {
        row: Object,
        column: Object
    },
    data() {
        return {
            statusMap: {
                1: this.$t('已随访'),
                2: this.$t('窗口内待随访'),
                3: this.$t('超期未随访'),
                4: this.$t('窗口外')
            },
            statusClass: {
                1: 'success',   // 已随访
                2: 'primary',   // 窗口内待随访
                3: 'danger',    // 超期未随访
                4: 'info'       // 窗口外
            },
            formStatusMap: visitStatusMap
        }
    },
    computed: {
        visits() {
            const temArr = this.row.visitList.filter(item => item.uid === this.column.property)

            temArr.map(visit => {
                // 对不同状态的表带进行一个叠加计数
                let groupedByAuditStatus = {}
                if (visit.formStatus) {
                    groupedByAuditStatus = visit.formStatus.reduce((acc, item) => {
                        const key = item.auditStatus;
                        if (!acc[key]) {
                            acc[key] = [];
                        }
                        acc[key].push(item);
                        return acc;
                    }, {});

                    // 删除掉值为空数组的key
                    Object.keys(groupedByAuditStatus).forEach(key => {
                        if (groupedByAuditStatus[key].length === 0) {
                            delete groupedByAuditStatus[key];
                        }
                    });
                }

                visit.groupedFormMap = groupedByAuditStatus
            })

            console.log(temArr, '85')
            return temArr
        }
    },
    methods: {
        clickVisit(visit) {
            this.$emit('clickVisit',visit)
        }
    }
}
</script>

<style lang="scss" scoped>
.visit-progress-cell {
    display: flex;
    height: 20px;
    line-height: 20px;

    .visit-cell {
        display: flex;
        align-items: center;
        padding: 10px 0;
        padding-right: 5px;
        margin: 0 5px;

        .gorm-status-group {
            display: flex;
            align-items: center;
            .status-img {
                width: 18px;
                height: 18px;
            }
            .status-num {
                font-size: 12px;
                padding-top: 6px;
            }
        }
    }
}


.success {
    background-color: #5db42f;
}

.primary {
    background-color: #409eff;
}

.warning {
    background-color: #EC9D00;
}

.danger {
    background-color: #F25757;
}

.info {
    background-color: #e9e9eb;
}
</style>